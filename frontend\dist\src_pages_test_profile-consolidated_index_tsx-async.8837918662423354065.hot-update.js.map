{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.8837918662423354065.hot-update.js", "src/pages/test/profile-consolidated/UserProfileCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15264411200412329310';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import React from 'react';\r\nimport { Row, Col, Card, Avatar, Statistic, List, Typography } from 'antd';\r\nimport { \r\n  UserOutlined, \r\n  IdcardOutlined, \r\n  CalendarOutlined, \r\n  ClockCircleOutlined, \r\n  TeamOutlined, \r\n  CarOutlined, \r\n  UserSwitchOutlined,\r\n  BellOutlined,\r\n  WarningOutlined\r\n} from '@ant-design/icons';\r\n\r\nconst { Title, Text, Paragraph } = Typography;\r\n\r\nconst UserDashboard = () => {\r\n  // 用户信息数据\r\n  const userData = {\r\n    name: 'zhang<PERSON>',\r\n    email: 'zhang<PERSON>@qq.com',\r\n    userId: '123456',\r\n    registerDate: '2025-07-23',\r\n    lastLogin: '2025-07-28 10:44:20',\r\n    teamsCount: 2,\r\n    lastTeam: '123'\r\n  };\r\n\r\n  // 统计卡片数据\r\n  const statsData = [\r\n    { icon: <CarOutlined />, name: '车辆', value: 0, color: '#1890ff' },\r\n    { icon: <UserSwitchOutlined />, name: '人员', value: 0, color: '#52c41a' },\r\n    { icon: <BellOutlined />, name: '预警', value: 0, color: '#faad14' },\r\n    { icon: <WarningOutlined />, name: '告警', value: 0, color: '#ff4d4f' }\r\n  ];\r\n\r\n  // 用户详细信息\r\n  const userDetails = [\r\n    { icon: <IdcardOutlined />, label: '用户编号', value: userData.userId },\r\n    { icon: <CalendarOutlined />, label: '注册日期', value: userData.registerDate },\r\n    { icon: <ClockCircleOutlined />, label: '最后登录时间', value: userData.lastLogin },\r\n    { icon: <TeamOutlined />, label: '团队总数', value: userData.teamsCount },\r\n    { icon: <TeamOutlined />, label: '最后登录团队', value: userData.lastTeam }\r\n  ];\r\n\r\n  return (\r\n    <div style={{ \r\n      background: '#f5f7fa', \r\n      minHeight: '100vh', \r\n      padding: '24px',\r\n      maxWidth: '1200px',\r\n      margin: '0 auto'\r\n    }}>\r\n      {/* 标题区域 */}\r\n      <header style={{ marginBottom: '32px' }}>\r\n        <Title level={2} style={{ color: '#2c3e50', fontWeight: 600 }}>\r\n          用户信息\r\n        </Title>\r\n        <Text type=\"secondary\">查看用户账户的详细信息和相关统计</Text>\r\n      </header>\r\n\r\n      {/* 内容区域 */}\r\n      <Row gutter={24} style={{ marginBottom: '24px' }}>\r\n        {/* 用户信息卡片 */}\r\n        <Col xs={24} md={16}>\r\n          <Card \r\n            bordered={false}\r\n            style={{ \r\n              borderRadius: '12px', \r\n              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',\r\n              marginBottom: '24px'\r\n            }}\r\n          >\r\n            {/* 用户个人资料 */}\r\n            <div style={{ \r\n              display: 'flex', \r\n              alignItems: 'center', \r\n              marginBottom: '30px'\r\n            }}>\r\n              <Avatar \r\n                size={80} \r\n                style={{ \r\n                  background: 'linear-gradient(135deg, #3498db, #1a5276)',\r\n                  fontSize: '36px',\r\n                  fontWeight: 'bold'\r\n                }}\r\n              >\r\n                {userData.name.charAt(0)}\r\n              </Avatar>\r\n              \r\n              <div style={{ marginLeft: '24px' }}>\r\n                <Title level={3} style={{ \r\n                  marginBottom: '4px',\r\n                  color: '#2c3e50',\r\n                  fontWeight: 600\r\n                }}>\r\n                  {userData.name}\r\n                </Title>\r\n                <Text type=\"secondary\" style={{ fontSize: '16px' }}>\r\n                  {userData.email}\r\n                </Text>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* 用户详细信息列表 */}\r\n            <List\r\n              grid={{ \r\n                gutter: 16,\r\n                xs: 1,\r\n                sm: 2\r\n              }}\r\n              dataSource={userDetails}\r\n              renderItem={item => (\r\n                <List.Item>\r\n                  <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                    <div \r\n                      style={{ \r\n                        width: '44px',\r\n                        height: '44px',\r\n                        background: '#ecf0f1',\r\n                        borderRadius: '10px',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center',\r\n                        marginRight: '15px',\r\n                        color: '#3498db',\r\n                        fontSize: '18px'\r\n                      }}\r\n                    >\r\n                      {item.icon}\r\n                    </div>\r\n                    <div>\r\n                      <Text type=\"secondary\" style={{ \r\n                        fontSize: '14px', \r\n                        display: 'block',\r\n                        marginBottom: '4px'\r\n                      }}>\r\n                        {item.label}\r\n                      </Text>\r\n                      <Text strong style={{ \r\n                        fontSize: '18px',\r\n                        color: '#2c3e50'\r\n                      }}>\r\n                        {item.value}\r\n                      </Text>\r\n                    </div>\r\n                  </div>\r\n                </List.Item>\r\n              )}\r\n            />\r\n          </Card>\r\n        </Col>\r\n\r\n        {/* 统计卡片区域 */}\r\n        <Col xs={24} md={8}>\r\n          <Row gutter={[16, 16]}>\r\n            {statsData.map((stat, index) => (\r\n              <Col xs={12} key={index}>\r\n                <Card \r\n                  bordered={false} \r\n                  hoverable\r\n                  style={{ \r\n                    borderRadius: '12px', \r\n                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',\r\n                    height: '100%',\r\n                    textAlign: 'center',\r\n                    transition: 'transform 0.3s',\r\n                  }}\r\n                  bodyStyle={{ padding: '24px' }}\r\n                  onClick={() => console.log(`${stat.name} clicked`)}\r\n                >\r\n                  <div style={{ \r\n                    fontSize: '36px', \r\n                    marginBottom: '15px',\r\n                    display: 'block',\r\n                    color: stat.color\r\n                  }}>\r\n                    {stat.icon}\r\n                  </div>\r\n                  <Text type=\"secondary\" style={{ \r\n                    fontSize: '16px',\r\n                    display: 'block',\r\n                    marginBottom: '12px'\r\n                  }}>\r\n                    {stat.name}\r\n                  </Text>\r\n                  <Statistic \r\n                    value={stat.value} \r\n                    valueStyle={{ \r\n                      fontSize: '36px', \r\n                      fontWeight: 700,\r\n                      color: '#2c3e50'\r\n                    }} \r\n                  />\r\n                </Card>\r\n              </Col>\r\n            ))}\r\n          </Row>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* 页脚 */}\r\n      <footer style={{ \r\n        marginTop: '40px', \r\n        textAlign: 'center',\r\n        color: '#7f8c8d',\r\n        fontSize: '14px',\r\n        padding: '20px 0'\r\n      }}>\r\n        <Paragraph>\r\n          © 2025 用户信息管理系统 | 最后更新: {new Date().toLocaleDateString('zh-CN')}\r\n        </Paragraph>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserDashboard;\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCsNb;;;2BAAA;;;;;;;mFAzNkB;yCACkD;0CAW7D;;;;;;;;;YAEP,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;YAE7C,MAAM,gBAAgB;gBACpB,SAAS;gBACT,MAAM,WAAW;oBACf,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,WAAW;oBACX,YAAY;oBACZ,UAAU;gBACZ;gBAEA,SAAS;gBACT,MAAM,YAAY;oBAChB;wBAAE,oBAAM,2BAAC,kBAAW;;;;;wBAAK,MAAM;wBAAM,OAAO;wBAAG,OAAO;oBAAU;oBAChE;wBAAE,oBAAM,2BAAC,yBAAkB;;;;;wBAAK,MAAM;wBAAM,OAAO;wBAAG,OAAO;oBAAU;oBACvE;wBAAE,oBAAM,2BAAC,mBAAY;;;;;wBAAK,MAAM;wBAAM,OAAO;wBAAG,OAAO;oBAAU;oBACjE;wBAAE,oBAAM,2BAAC,sBAAe;;;;;wBAAK,MAAM;wBAAM,OAAO;wBAAG,OAAO;oBAAU;iBACrE;gBAED,SAAS;gBACT,MAAM,cAAc;oBAClB;wBAAE,oBAAM,2BAAC,qBAAc;;;;;wBAAK,OAAO;wBAAQ,OAAO,SAAS,MAAM;oBAAC;oBAClE;wBAAE,oBAAM,2BAAC,uBAAgB;;;;;wBAAK,OAAO;wBAAQ,OAAO,SAAS,YAAY;oBAAC;oBAC1E;wBAAE,oBAAM,2BAAC,0BAAmB;;;;;wBAAK,OAAO;wBAAU,OAAO,SAAS,SAAS;oBAAC;oBAC5E;wBAAE,oBAAM,2BAAC,mBAAY;;;;;wBAAK,OAAO;wBAAQ,OAAO,SAAS,UAAU;oBAAC;oBACpE;wBAAE,oBAAM,2BAAC,mBAAY;;;;;wBAAK,OAAO;wBAAU,OAAO,SAAS,QAAQ;oBAAC;iBACrE;gBAED,qBACE,2BAAC;oBAAI,OAAO;wBACV,YAAY;wBACZ,WAAW;wBACX,SAAS;wBACT,UAAU;wBACV,QAAQ;oBACV;;sCAEE,2BAAC;4BAAO,OAAO;gCAAE,cAAc;4BAAO;;8CACpC,2BAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAW,YAAY;oCAAI;8CAAG;;;;;;8CAG/D,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;;;sCAIzB,2BAAC,SAAG;4BAAC,QAAQ;4BAAI,OAAO;gCAAE,cAAc;4BAAO;;8CAE7C,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;8CACf,cAAA,2BAAC,UAAI;wCACH,UAAU;wCACV,OAAO;4CACL,cAAc;4CACd,WAAW;4CACX,cAAc;wCAChB;;0DAGA,2BAAC;gDAAI,OAAO;oDACV,SAAS;oDACT,YAAY;oDACZ,cAAc;gDAChB;;kEACE,2BAAC,YAAM;wDACL,MAAM;wDACN,OAAO;4DACL,YAAY;4DACZ,UAAU;4DACV,YAAY;wDACd;kEAEC,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;kEAGxB,2BAAC;wDAAI,OAAO;4DAAE,YAAY;wDAAO;;0EAC/B,2BAAC;gEAAM,OAAO;gEAAG,OAAO;oEACtB,cAAc;oEACd,OAAO;oEACP,YAAY;gEACd;0EACG,SAAS,IAAI;;;;;;0EAEhB,2BAAC;gEAAK,MAAK;gEAAY,OAAO;oEAAE,UAAU;gEAAO;0EAC9C,SAAS,KAAK;;;;;;;;;;;;;;;;;;0DAMrB,2BAAC,UAAI;gDACH,MAAM;oDACJ,QAAQ;oDACR,IAAI;oDACJ,IAAI;gDACN;gDACA,YAAY;gDACZ,YAAY,CAAA,qBACV,2BAAC,UAAI,CAAC,IAAI;kEACR,cAAA,2BAAC;4DAAI,OAAO;gEAAE,SAAS;gEAAQ,YAAY;4DAAS;;8EAClD,2BAAC;oEACC,OAAO;wEACL,OAAO;wEACP,QAAQ;wEACR,YAAY;wEACZ,cAAc;wEACd,SAAS;wEACT,YAAY;wEACZ,gBAAgB;wEAChB,aAAa;wEACb,OAAO;wEACP,UAAU;oEACZ;8EAEC,KAAK,IAAI;;;;;;8EAEZ,2BAAC;;sFACC,2BAAC;4EAAK,MAAK;4EAAY,OAAO;gFAC5B,UAAU;gFACV,SAAS;gFACT,cAAc;4EAChB;sFACG,KAAK,KAAK;;;;;;sFAEb,2BAAC;4EAAK,MAAM;4EAAC,OAAO;gFAClB,UAAU;gFACV,OAAO;4EACT;sFACG,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW3B,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;8CACf,cAAA,2BAAC,SAAG;wCAAC,QAAQ;4CAAC;4CAAI;yCAAG;kDAClB,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,2BAAC,SAAG;gDAAC,IAAI;0DACP,cAAA,2BAAC,UAAI;oDACH,UAAU;oDACV,SAAS;oDACT,OAAO;wDACL,cAAc;wDACd,WAAW;wDACX,QAAQ;wDACR,WAAW;wDACX,YAAY;oDACd;oDACA,WAAW;wDAAE,SAAS;oDAAO;oDAC7B,SAAS,IAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;;sEAEjD,2BAAC;4DAAI,OAAO;gEACV,UAAU;gEACV,cAAc;gEACd,SAAS;gEACT,OAAO,KAAK,KAAK;4DACnB;sEACG,KAAK,IAAI;;;;;;sEAEZ,2BAAC;4DAAK,MAAK;4DAAY,OAAO;gEAC5B,UAAU;gEACV,SAAS;gEACT,cAAc;4DAChB;sEACG,KAAK,IAAI;;;;;;sEAEZ,2BAAC,eAAS;4DACR,OAAO,KAAK,KAAK;4DACjB,YAAY;gEACV,UAAU;gEACV,YAAY;gEACZ,OAAO;4DACT;;;;;;;;;;;;+CAnCY;;;;;;;;;;;;;;;;;;;;;sCA6C1B,2BAAC;4BAAO,OAAO;gCACb,WAAW;gCACX,WAAW;gCACX,OAAO;gCACP,UAAU;gCACV,SAAS;4BACX;sCACE,cAAA,2BAAC;;oCAAU;oCACgB,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;YAKjE;iBAvMM;gBAyMN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDtND;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}