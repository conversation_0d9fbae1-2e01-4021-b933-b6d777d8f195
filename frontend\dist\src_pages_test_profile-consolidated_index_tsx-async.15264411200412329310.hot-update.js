globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/UserProfileCard.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const { Title, Text, Paragraph } = _antd.Typography;
            const UserDashboard = ()=>{
                // 用户信息数据
                const userData = {
                    name: 'zhangsan',
                    email: '<EMAIL>',
                    userId: '123456',
                    registerDate: '2025-07-23',
                    lastLogin: '2025-07-28 10:44:20',
                    teamsCount: 2,
                    lastTeam: '123'
                };
                // 统计卡片数据
                const statsData = [
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 31,
                            columnNumber: 13
                        }, this),
                        name: '车辆',
                        value: 0,
                        color: '#1890ff'
                    },
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserSwitchOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 32,
                            columnNumber: 13
                        }, this),
                        name: '人员',
                        value: 0,
                        color: '#52c41a'
                    },
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BellOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 33,
                            columnNumber: 13
                        }, this),
                        name: '预警',
                        value: 0,
                        color: '#faad14'
                    },
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 34,
                            columnNumber: 13
                        }, this),
                        name: '告警',
                        value: 0,
                        color: '#ff4d4f'
                    }
                ];
                // 用户详细信息
                const userDetails = [
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.IdcardOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 39,
                            columnNumber: 13
                        }, this),
                        label: '用户编号',
                        value: userData.userId
                    },
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 40,
                            columnNumber: 13
                        }, this),
                        label: '注册日期',
                        value: userData.registerDate
                    },
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 41,
                            columnNumber: 13
                        }, this),
                        label: '最后登录时间',
                        value: userData.lastLogin
                    },
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 42,
                            columnNumber: 13
                        }, this),
                        label: '团队总数',
                        value: userData.teamsCount
                    },
                    {
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 43,
                            columnNumber: 13
                        }, this),
                        label: '最后登录团队',
                        value: userData.lastTeam
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        background: '#f5f7fa',
                        minHeight: '100vh',
                        padding: '24px',
                        maxWidth: '1200px',
                        margin: '0 auto'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("header", {
                            style: {
                                marginBottom: '32px'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 2,
                                    style: {
                                        color: '#2c3e50',
                                        fontWeight: 600
                                    },
                                    children: "用户信息"
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 56,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "查看用户账户的详细信息和相关统计"
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 59,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 55,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: 24,
                            style: {
                                marginBottom: '24px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 24,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: 24,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            md: 16,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                bordered: false,
                                                style: {
                                                    borderRadius: '12px',
                                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                                                    height: '100%'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            marginBottom: '30px'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                                size: 80,
                                                                style: {
                                                                    background: 'linear-gradient(135deg, #3498db, #1a5276)',
                                                                    fontSize: '36px',
                                                                    fontWeight: 'bold'
                                                                },
                                                                children: userData.name.charAt(0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 83,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    marginLeft: '24px'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                        level: 3,
                                                                        style: {
                                                                            marginBottom: '4px',
                                                                            color: '#2c3e50',
                                                                            fontWeight: 600
                                                                        },
                                                                        children: userData.name
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 95,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: '16px'
                                                                        },
                                                                        children: userData.email
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 102,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 94,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 78,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                                        grid: {
                                                            gutter: 16,
                                                            xs: 1,
                                                            sm: 2
                                                        },
                                                        dataSource: userDetails,
                                                        renderItem: (item)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        display: 'flex',
                                                                        alignItems: 'center'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                width: '44px',
                                                                                height: '44px',
                                                                                background: '#ecf0f1',
                                                                                borderRadius: '10px',
                                                                                display: 'flex',
                                                                                alignItems: 'center',
                                                                                justifyContent: 'center',
                                                                                marginRight: '15px',
                                                                                color: '#3498db',
                                                                                fontSize: '18px'
                                                                            },
                                                                            children: item.icon
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 119,
                                                                            columnNumber: 25
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    type: "secondary",
                                                                                    style: {
                                                                                        fontSize: '14px',
                                                                                        display: 'block',
                                                                                        marginBottom: '4px'
                                                                                    },
                                                                                    children: item.label
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                    lineNumber: 136,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    strong: true,
                                                                                    style: {
                                                                                        fontSize: '18px',
                                                                                        color: '#2c3e50'
                                                                                    },
                                                                                    children: item.value
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                    lineNumber: 143,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 135,
                                                                            columnNumber: 25
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 118,
                                                                    columnNumber: 23
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 117,
                                                                columnNumber: 21
                                                            }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 109,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 69,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 68,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            md: 8,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                gutter: [
                                                    16,
                                                    16
                                                ],
                                                children: statsData.map((stat, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 24,
                                                        md: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                            bordered: false,
                                                            hoverable: true,
                                                            style: {
                                                                borderRadius: '12px',
                                                                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                                                                height: '100%',
                                                                textAlign: 'center',
                                                                transition: 'transform 0.3s'
                                                            },
                                                            bodyStyle: {
                                                                padding: '24px'
                                                            },
                                                            onClick: ()=>console.log(`${stat.name} clicked`),
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: '36px',
                                                                        marginBottom: '15px',
                                                                        display: 'block',
                                                                        color: stat.color
                                                                    },
                                                                    children: stat.icon
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 175,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    style: {
                                                                        fontSize: '16px',
                                                                        display: 'block',
                                                                        marginBottom: '12px'
                                                                    },
                                                                    children: stat.name
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 183,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                                                    value: stat.value,
                                                                    valueStyle: {
                                                                        fontSize: '36px',
                                                                        fontWeight: 700,
                                                                        color: '#2c3e50'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 190,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 162,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, index, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 161,
                                                        columnNumber: 19
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 159,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 158,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 66,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 65,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 63,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("footer", {
                            style: {
                                marginTop: '40px',
                                textAlign: 'center',
                                color: '#7f8c8d',
                                fontSize: '14px',
                                padding: '20px 0'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    "© 2025 用户信息管理系统 | 最后更新: ",
                                    new Date().toLocaleDateString('zh-CN')
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 215,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 208,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                    lineNumber: 47,
                    columnNumber: 5
                }, this);
            };
            _c = UserDashboard;
            var _default = UserDashboard;
            var _c;
            $RefreshReg$(_c, "UserDashboard");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '17461326414255005323';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.15264411200412329310.hot-update.js.map