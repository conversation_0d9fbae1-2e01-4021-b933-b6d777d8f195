globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/UserProfileCard.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const UserDashboard = ()=>{
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        padding: '24px',
                        maxWidth: '1200px',
                        margin: '0 auto'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        style: {
                            width: '100%',
                            borderRadius: '12px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginBottom: '24px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("h1", {
                                        style: {
                                            fontSize: '24px',
                                            fontWeight: 600,
                                            marginBottom: '8px',
                                            color: '#2c3e50'
                                        },
                                        children: "用户信息"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 22,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                        style: {
                                            color: '#7f8c8d'
                                        },
                                        children: "查看用户账户的详细信息和相关统计"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 23,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 21,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                gutter: 24,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        span: 16,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                            style: {
                                                width: '100%',
                                                border: 'none',
                                                boxShadow: 'none'
                                            },
                                            bodyStyle: {
                                                padding: '24px'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        marginBottom: '30px'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                            style: {
                                                                backgroundColor: '#1890ff',
                                                                fontSize: '24px',
                                                                width: '80px',
                                                                height: '80px',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center'
                                                            },
                                                            children: "Z"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 35,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                marginLeft: '24px'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("h2", {
                                                                    style: {
                                                                        fontSize: '24px',
                                                                        fontWeight: 600,
                                                                        marginBottom: '4px',
                                                                        color: '#2c3e50'
                                                                    },
                                                                    children: "zhangsan"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 49,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        color: '#7f8c8d'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                            style: {
                                                                                marginRight: '8px'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 53,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                            children: "<EMAIL>"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 54,
                                                                            columnNumber: 21
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 52,
                                                                    columnNumber: 19
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 48,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 34,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                                    style: {
                                                        margin: '24px 0'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 59,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                    gutter: [
                                                        24,
                                                        24
                                                    ],
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            span: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(DetailCard, {
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.NumberOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 65,
                                                                    columnNumber: 27
                                                                }, void 0),
                                                                title: "用户编号",
                                                                value: "123456"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 64,
                                                                columnNumber: 19
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 63,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            span: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(DetailCard, {
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 73,
                                                                    columnNumber: 27
                                                                }, void 0),
                                                                title: "注册日期",
                                                                value: "2025-07-23"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 72,
                                                                columnNumber: 19
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 71,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            span: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(DetailCard, {
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 81,
                                                                    columnNumber: 27
                                                                }, void 0),
                                                                title: "团队总数",
                                                                value: "2"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 80,
                                                                columnNumber: 19
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 79,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            span: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(DetailCard, {
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 89,
                                                                    columnNumber: 27
                                                                }, void 0),
                                                                title: "最后登录时间",
                                                                value: "2025-07-28 10:44:20"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 88,
                                                                columnNumber: 19
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 87,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            span: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(DetailCard, {
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 97,
                                                                    columnNumber: 27
                                                                }, void 0),
                                                                title: "最后登录团队",
                                                                value: "123"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 96,
                                                                columnNumber: 19
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 95,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 62,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 29,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 28,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        span: 8,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                            gutter: [
                                                0,
                                                24
                                            ],
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    span: 24,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(StatCard, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                            style: {
                                                                color: '#3498db',
                                                                fontSize: '36px'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 111,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        title: "车辆",
                                                        value: 0
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 110,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 109,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    span: 24,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(StatCard, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {
                                                            style: {
                                                                color: '#2ecc71',
                                                                fontSize: '36px'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 119,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        title: "人员",
                                                        value: 0
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 118,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 117,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    span: 24,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(StatCard, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BellOutlined, {
                                                            style: {
                                                                color: '#f39c12',
                                                                fontSize: '36px'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 127,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        title: "预警",
                                                        value: 0
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 126,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 125,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    span: 24,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(StatCard, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                            style: {
                                                                color: '#e74c3c',
                                                                fontSize: '36px'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 135,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        title: "告警",
                                                        value: 0
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 134,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 133,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 108,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 107,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 26,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '36px',
                                    color: '#7f8c8d',
                                    fontSize: '14px',
                                    textAlign: 'center'
                                },
                                children: "© 2025 用户信息管理系统 | 最后更新: 2025-07-28"
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 145,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 19,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                    lineNumber: 18,
                    columnNumber: 5
                }, this);
            };
            _c = UserDashboard;
            // 自定义信息卡片组件
            const DetailCard = ({ icon, title, value })=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#f8f9fa',
                        padding: '12px',
                        borderRadius: '8px'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                width: '40px',
                                height: '40px',
                                backgroundColor: '#e9ecef',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginRight: '12px',
                                fontSize: '18px',
                                color: '#495057'
                            },
                            children: icon
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 162,
                            columnNumber: 5
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        color: '#6c757d',
                                        fontSize: '14px'
                                    },
                                    children: title
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 177,
                                    columnNumber: 7
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontWeight: 500,
                                        fontSize: '18px',
                                        color: '#212529'
                                    },
                                    children: value
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 178,
                                    columnNumber: 7
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 176,
                            columnNumber: 5
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                    lineNumber: 155,
                    columnNumber: 3
                }, this);
            _c1 = DetailCard;
            // 自定义统计卡片组件
            const StatCard = ({ icon, title, value })=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    style: {
                        borderRadius: '12px',
                        textAlign: 'center',
                        transition: 'transform 0.3s'
                    },
                    hoverable: true,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: '16px'
                            },
                            children: icon
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 193,
                            columnNumber: 5
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                            title: title,
                            value: value
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 194,
                            columnNumber: 5
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                    lineNumber: 185,
                    columnNumber: 3
                }, this);
            _c2 = StatCard;
            var _default = UserDashboard;
            var _c;
            var _c1;
            var _c2;
            $RefreshReg$(_c, "UserDashboard");
            $RefreshReg$(_c1, "DetailCard");
            $RefreshReg$(_c2, "StatCard");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15109464735480349106';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.17461326414255005323.hot-update.js.map