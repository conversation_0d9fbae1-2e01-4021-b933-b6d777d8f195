globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/UserProfileCard.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { Step } = _antd.Steps;
            const UserProfileCard = ()=>{
                var _subscriptionPlans_find;
                _s();
                // 用户详细信息状态
                const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: "",
                    position: "",
                    email: "",
                    telephone: "",
                    registerDate: "",
                    lastLoginTime: "",
                    lastLoginTeam: "",
                    teamCount: 0,
                    avatar: ""
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // 个人统计数据状态
                const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // 订阅计划数据
                const subscriptionPlans = [
                    {
                        id: "basic",
                        name: "基础版",
                        price: 0,
                        description: "适合小团队使用",
                        features: [
                            "最多5个团队",
                            "最多20辆车辆",
                            "基础安全监控",
                            "基本报告功能"
                        ]
                    },
                    {
                        id: "professional",
                        name: "专业版",
                        price: 199,
                        description: "适合中小型企业",
                        features: [
                            "最多20个团队",
                            "最多100辆车辆",
                            "高级安全监控",
                            "详细分析报告",
                            "设备状态预警",
                            "优先技术支持"
                        ]
                    },
                    {
                        id: "enterprise",
                        name: "企业版",
                        price: 499,
                        description: "适合大型企业",
                        features: [
                            "不限团队数量",
                            "不限车辆数量",
                            "AI安全分析",
                            "实时监控告警",
                            "定制化报告",
                            "专属客户经理",
                            "24/7技术支持"
                        ]
                    }
                ];
                // 当前订阅信息
                const currentSubscription = {
                    planId: "basic",
                    expires: "2025-12-31"
                };
                // 状态管理
                const [editProfileModalVisible, setEditProfileModalVisible] = (0, _react.useState)(false);
                const [subscriptionModalVisible, setSubscriptionModalVisible] = (0, _react.useState)(false);
                const [logoutModalVisible, setLogoutModalVisible] = (0, _react.useState)(false);
                const [logoutLoading, setLogoutLoading] = (0, _react.useState)(false);
                const [currentStep, setCurrentStep] = (0, _react.useState)(0);
                const [editProfileForm] = _antd.Form.useForm();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                // 获取用户数据
                (0, _react.useEffect)(()=>{
                    console.log('UserProfileCard: useEffect 开始执行');
                    const fetchUserData = async ()=>{
                        try {
                            console.log('UserProfileCard: 开始获取用户数据');
                            // 分别获取用户详细信息和统计数据，避免一个失败影响另一个
                            const userDetailPromise = _user.UserService.getUserProfileDetail().catch((error)=>{
                                console.error('获取用户详细信息失败:', error);
                                setUserInfoError('获取用户详细信息失败，请稍后重试');
                                return null;
                            });
                            const statsPromise = _user.UserService.getUserPersonalStats().catch((error)=>{
                                console.error('获取统计数据失败:', error);
                                setStatsError('获取统计数据失败，请稍后重试');
                                return null;
                            });
                            const [userDetail, stats] = await Promise.all([
                                userDetailPromise,
                                statsPromise
                            ]);
                            if (userDetail) {
                                console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
                                setUserInfo(userDetail);
                                setUserInfoError(null);
                            }
                            if (stats) {
                                console.log('UserProfileCard: 获取到统计数据:', stats);
                                setPersonalStats(stats);
                                setStatsError(null);
                            }
                        } catch (error) {
                            console.error('获取用户数据时发生未知错误:', error);
                            setUserInfoError('获取用户数据失败，请刷新页面重试');
                            setStatsError('获取统计数据失败，请刷新页面重试');
                        } finally{
                            setUserInfoLoading(false);
                            setStatsLoading(false);
                        }
                    };
                    fetchUserData();
                }, []);
                // 退出登录处理函数
                const handleLogout = async ()=>{
                    try {
                        setLogoutLoading(true);
                        // 调用退出登录API
                        await _services.AuthService.logout();
                        // 清除 initialState
                        if (setInitialState) await setInitialState({
                            currentUser: undefined,
                            currentTeam: undefined
                        });
                        // 跳转到登录页面
                        _max.history.push('/user/login');
                    } catch (error) {
                        console.error('退出登录失败:', error);
                        // 即使API调用失败，也要清除本地状态并跳转
                        if (setInitialState) await setInitialState({
                            currentUser: undefined,
                            currentTeam: undefined
                        });
                        _max.history.push('/user/login');
                    } finally{
                        setLogoutLoading(false);
                        setLogoutModalVisible(false);
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            className: "dashboard-card",
                            style: {
                                borderRadius: 16,
                                boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
                                border: "1px solid rgba(0,0,0,0.06)",
                                background: "#ffffff",
                                position: "relative",
                                overflow: "hidden"
                            },
                            bodyStyle: {
                                padding: "24px 24px 20px"
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    gap: 4,
                                    align: "center",
                                    style: {
                                        position: "absolute",
                                        top: 16,
                                        right: 16,
                                        zIndex: 10
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                            overlay: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Menu, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Menu.Item, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 240,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        onClick: ()=>{
                                                            setEditProfileModalVisible(true);
                                                            setCurrentStep(0);
                                                            editProfileForm.setFieldsValue({
                                                                name: userInfo.name,
                                                                email: userInfo.email,
                                                                telephone: userInfo.telephone
                                                            });
                                                        },
                                                        children: "修改资料"
                                                    }, "editProfile", false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 238,
                                                        columnNumber: 17
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Menu.Item, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TagOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 255,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        onClick: ()=>setSubscriptionModalVisible(true),
                                                        children: "订阅套餐"
                                                    }, "subscription", false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 253,
                                                        columnNumber: 17
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 237,
                                                columnNumber: 15
                                            }, void 0),
                                            trigger: [
                                                "click"
                                            ],
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "text",
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                                                    style: {
                                                        fontSize: 16,
                                                        color: "#8c8c8c"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 21
                                                }, void 0),
                                                style: {
                                                    padding: "6px 8px",
                                                    borderRadius: 8,
                                                    transition: "all 0.2s ease"
                                                },
                                                className: "hover:bg-gray-50"
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 264,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 235,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {
                                                style: {
                                                    fontSize: 16,
                                                    color: "#8c8c8c"
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 278,
                                                columnNumber: 19
                                            }, void 0),
                                            onClick: ()=>setLogoutModalVisible(true),
                                            style: {
                                                padding: "6px 8px",
                                                borderRadius: 8,
                                                transition: "all 0.2s ease"
                                            },
                                            className: "hover:bg-gray-50"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 276,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 225,
                                    columnNumber: 9
                                }, this),
                                userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                    message: "用户信息加载失败",
                                    description: userInfoError,
                                    type: "error",
                                    showIcon: true,
                                    style: {
                                        marginBottom: 16
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 291,
                                    columnNumber: 11
                                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                    spinning: userInfoLoading,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        size: 24,
                                        style: {
                                            width: "100%"
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                gutter: [
                                                    24,
                                                    16
                                                ],
                                                align: "middle",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 24,
                                                        sm: 4,
                                                        md: 3,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            justify: "center",
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                                size: 64,
                                                                src: userInfo.avatar,
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 308,
                                                                    columnNumber: 29
                                                                }, void 0),
                                                                style: {
                                                                    backgroundColor: "#1890ff",
                                                                    border: "3px solid #f0f7ff",
                                                                    boxShadow: "0 4px 12px rgba(24, 144, 255, 0.15)"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 305,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 304,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 303,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 24,
                                                        sm: 20,
                                                        md: 21,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            direction: "vertical",
                                                            size: 8,
                                                            style: {
                                                                width: "100%"
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                    level: 3,
                                                                    style: {
                                                                        margin: 0,
                                                                        color: "#262626",
                                                                        fontWeight: 600
                                                                    },
                                                                    children: userInfo.name || "加载中..."
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 319,
                                                                    columnNumber: 21
                                                                }, this),
                                                                userInfo.position && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    style: {
                                                                        fontSize: 14
                                                                    },
                                                                    children: userInfo.position
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 323,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    wrap: "wrap",
                                                                    gap: 8,
                                                                    style: {
                                                                        marginTop: 8
                                                                    },
                                                                    children: [
                                                                        userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                                style: {
                                                                                    fontSize: 12
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 332,
                                                                                columnNumber: 33
                                                                            }, void 0),
                                                                            color: "blue",
                                                                            style: {
                                                                                fontSize: 12,
                                                                                padding: "2px 8px",
                                                                                borderRadius: 6
                                                                            },
                                                                            children: userInfo.email
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 331,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                                style: {
                                                                                    fontSize: 12
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 341,
                                                                                columnNumber: 33
                                                                            }, void 0),
                                                                            color: "green",
                                                                            style: {
                                                                                fontSize: 12,
                                                                                padding: "2px 8px",
                                                                                borderRadius: 6
                                                                            },
                                                                            children: userInfo.telephone
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 340,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                                                style: {
                                                                                    fontSize: 12
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 350,
                                                                                columnNumber: 33
                                                                            }, void 0),
                                                                            color: "orange",
                                                                            style: {
                                                                                fontSize: 12,
                                                                                padding: "2px 8px",
                                                                                borderRadius: 6
                                                                            },
                                                                            children: [
                                                                                "注册于 ",
                                                                                userInfo.registerDate
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 349,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 329,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 318,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 317,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 302,
                                                columnNumber: 15
                                            }, this),
                                            statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                                message: "统计数据加载失败",
                                                description: statsError,
                                                type: "error",
                                                showIcon: true
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 364,
                                                columnNumber: 17
                                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                spinning: statsLoading,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                    gutter: [
                                                        16,
                                                        16
                                                    ],
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 12,
                                                            sm: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: "linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)",
                                                                    border: "1px solid #91d5ff",
                                                                    borderRadius: 12,
                                                                    textAlign: "center"
                                                                },
                                                                bodyStyle: {
                                                                    padding: "16px 12px"
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    direction: "vertical",
                                                                    size: 4,
                                                                    style: {
                                                                        width: "100%"
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                            style: {
                                                                                fontSize: 24,
                                                                                color: "#1890ff"
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 386,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: "#1890ff",
                                                                                fontWeight: 600,
                                                                                display: "block"
                                                                            },
                                                                            children: personalStats.vehicles
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 387,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: "#595959"
                                                                            },
                                                                            children: "车辆"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 398,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 385,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 375,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 374,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 12,
                                                            sm: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: "linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)",
                                                                    border: "1px solid #b7eb8f",
                                                                    borderRadius: 12,
                                                                    textAlign: "center"
                                                                },
                                                                bodyStyle: {
                                                                    padding: "16px 12px"
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    direction: "vertical",
                                                                    size: 4,
                                                                    style: {
                                                                        width: "100%"
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                            style: {
                                                                                fontSize: 24,
                                                                                color: "#52c41a"
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 416,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: "#52c41a",
                                                                                fontWeight: 600,
                                                                                display: "block"
                                                                            },
                                                                            children: personalStats.personnel
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 417,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: "#595959"
                                                                            },
                                                                            children: "人员"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 428,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 415,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 405,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 404,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 12,
                                                            sm: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: "linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%)",
                                                                    border: "1px solid #ffe58f",
                                                                    borderRadius: 12,
                                                                    textAlign: "center"
                                                                },
                                                                bodyStyle: {
                                                                    padding: "16px 12px"
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    direction: "vertical",
                                                                    size: 4,
                                                                    style: {
                                                                        width: "100%"
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {
                                                                            style: {
                                                                                fontSize: 24,
                                                                                color: "#faad14"
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 446,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: "#faad14",
                                                                                fontWeight: 600,
                                                                                display: "block"
                                                                            },
                                                                            children: personalStats.warnings
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 447,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: "#595959"
                                                                            },
                                                                            children: "预警"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 458,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 445,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 435,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 434,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 12,
                                                            sm: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: "linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%)",
                                                                    border: "1px solid #ffa39e",
                                                                    borderRadius: 12,
                                                                    textAlign: "center"
                                                                },
                                                                bodyStyle: {
                                                                    padding: "16px 12px"
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    direction: "vertical",
                                                                    size: 4,
                                                                    style: {
                                                                        width: "100%"
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {
                                                                            style: {
                                                                                fontSize: 24,
                                                                                color: "#ff4d4f"
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 476,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: "#ff4d4f",
                                                                                fontWeight: 600,
                                                                                display: "block"
                                                                            },
                                                                            children: personalStats.alerts
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 477,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: "#595959"
                                                                            },
                                                                            children: "告警"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                            lineNumber: 488,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 475,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 465,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 464,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 372,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 371,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                size: "small",
                                                style: {
                                                    background: "#fafafa",
                                                    border: "1px solid #f0f0f0",
                                                    borderRadius: 8
                                                },
                                                bodyStyle: {
                                                    padding: "12px 16px"
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                    gutter: [
                                                        16,
                                                        8
                                                    ],
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 24,
                                                            sm: 8,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                                        style: {
                                                                            color: "#8c8c8c",
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 509,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            "最后登录: ",
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                strong: true,
                                                                                children: userInfo.lastLoginTime || "暂无记录"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 511,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 510,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 508,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 507,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 24,
                                                            sm: 8,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                        style: {
                                                                            color: "#8c8c8c",
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 517,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            "登录团队: ",
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                strong: true,
                                                                                children: userInfo.lastLoginTeam || "暂无"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 519,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 518,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 516,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 515,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 24,
                                                            sm: 8,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                        style: {
                                                                            color: "#8c8c8c",
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 525,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            "团队总数: ",
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                strong: true,
                                                                                children: userInfo.teamCount
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 527,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 526,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 524,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 523,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 506,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 497,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 300,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 299,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 212,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "修改个人资料",
                            open: editProfileModalVisible,
                            onCancel: ()=>{
                                setEditProfileModalVisible(false);
                                setCurrentStep(0);
                            },
                            footer: [
                                currentStep === 1 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>setCurrentStep(0),
                                    children: "上一步"
                                }, "back", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 548,
                                    columnNumber: 13
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    onClick: ()=>{
                                        if (currentStep === 0) editProfileForm.validateFields().then(()=>{
                                            setCurrentStep(1);
                                        });
                                        else editProfileForm.validateFields().then((values)=>{
                                            console.log("个人资料表单值:", values);
                                            // 提交表单，这里简化处理，只输出到控制台
                                            setEditProfileModalVisible(false);
                                            setCurrentStep(0);
                                        });
                                    },
                                    children: currentStep === 0 ? "下一步" : "确定"
                                }, "submit", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 552,
                                    columnNumber: 11
                                }, void 0)
                            ],
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Steps, {
                                    current: currentStep,
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                            title: "填写信息"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 575,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                            title: "安全验证"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 576,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 574,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                    form: editProfileForm,
                                    layout: "vertical",
                                    hideRequiredMark: true,
                                    children: currentStep === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "name",
                                                label: "用户名",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入用户名"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入用户名"
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 587,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 582,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "email",
                                                label: "邮箱",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入邮箱地址"
                                                    },
                                                    {
                                                        type: "email",
                                                        message: "请输入有效的邮箱地址"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入邮箱地址"
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 597,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 589,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "telephone",
                                                label: "手机号",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入手机号"
                                                    },
                                                    {
                                                        pattern: /^1\d{10}$/,
                                                        message: "请输入有效的手机号"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入手机号"
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 607,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 599,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            textAlign: "center"
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    margin: "12px 0",
                                                    textAlign: "center"
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: [
                                                        "验证码已发送至您的手机号",
                                                        " ",
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: editProfileForm.getFieldValue("telephone")
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 615,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 613,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 612,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "verificationCode",
                                                label: "验证码",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入验证码"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入6位验证码",
                                                    maxLength: 6,
                                                    style: {
                                                        width: "50%",
                                                        textAlign: "center"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 623,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 618,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "link",
                                                style: {
                                                    padding: 0
                                                },
                                                children: "重新发送验证码"
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 629,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 611,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 579,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 539,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "订阅套餐",
                            open: subscriptionModalVisible,
                            onCancel: ()=>setSubscriptionModalVisible(false),
                            footer: null,
                            width: 800,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        background: "#f9f9f9",
                                        padding: 12,
                                        borderRadius: 8,
                                        marginBottom: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        justify: "space-between",
                                        align: "center",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                strong: true,
                                                children: "当前套餐: "
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 654,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                color: "green",
                                                style: {
                                                    marginLeft: 8,
                                                    fontSize: 13
                                                },
                                                children: (_subscriptionPlans_find = subscriptionPlans.find((p)=>p.id === currentSubscription.planId)) === null || _subscriptionPlans_find === void 0 ? void 0 : _subscriptionPlans_find.name
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 655,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: [
                                                    "到期时间: ",
                                                    currentSubscription.expires
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 662,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 653,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 645,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: 24,
                                    children: subscriptionPlans.map((plan)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            span: 8,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    height: "100%",
                                                    borderRadius: 8,
                                                    border: `1px solid ${plan.id === currentSubscription.planId ? "#52c41a" : "#d9d9d9"}`,
                                                    background: "#fff",
                                                    position: "relative",
                                                    overflow: "hidden"
                                                },
                                                children: [
                                                    plan.id === currentSubscription.planId && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                        color: "green",
                                                        style: {
                                                            position: "absolute",
                                                            top: -10,
                                                            right: -10,
                                                            borderRadius: 2,
                                                            boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
                                                        },
                                                        children: "当前套餐"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 686,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            padding: 16
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                level: 4,
                                                                style: {
                                                                    textAlign: "center",
                                                                    margin: "12px 0 8px"
                                                                },
                                                                children: plan.name
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 700,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                vertical: true,
                                                                align: "center",
                                                                style: {
                                                                    marginBottom: 12
                                                                },
                                                                children: [
                                                                    plan.price > 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                                level: 2,
                                                                                style: {
                                                                                    marginBottom: 0
                                                                                },
                                                                                children: [
                                                                                    "¥",
                                                                                    plan.price
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 709,
                                                                                columnNumber: 25
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                type: "secondary",
                                                                                children: "/月"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 712,
                                                                                columnNumber: 25
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                        level: 2,
                                                                        style: {
                                                                            color: "#52c41a",
                                                                            marginBottom: 0
                                                                        },
                                                                        children: "免费"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 715,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            marginTop: 4
                                                                        },
                                                                        children: plan.description
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 722,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 706,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                                                style: {
                                                                    margin: "8px 0"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 727,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    minHeight: 170
                                                                },
                                                                children: plan.features.map((feature, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                        align: "start",
                                                                        style: {
                                                                            marginBottom: 6
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                                style: {
                                                                                    color: "#52c41a",
                                                                                    marginRight: 8,
                                                                                    marginTop: 4
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 736,
                                                                                columnNumber: 25
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                children: feature
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                                lineNumber: 743,
                                                                                columnNumber: 25
                                                                            }, this)
                                                                        ]
                                                                    }, index, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 731,
                                                                        columnNumber: 23
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 729,
                                                                columnNumber: 19
                                                            }, this),
                                                            plan.id !== currentSubscription.planId ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                type: "primary",
                                                                block: true,
                                                                style: {
                                                                    marginTop: 12,
                                                                    boxShadow: "0 2px 8px rgba(24, 144, 255, 0.3)"
                                                                },
                                                                onClick: ()=>{
                                                                    console.log("选择套餐:", plan);
                                                                    setSubscriptionModalVisible(false);
                                                                },
                                                                children: "立即订阅"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 749,
                                                                columnNumber: 21
                                                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                block: true,
                                                                style: {
                                                                    marginTop: 12,
                                                                    background: "#f6ffed",
                                                                    borderColor: "#b7eb8f",
                                                                    color: "#389e0d"
                                                                },
                                                                disabled: true,
                                                                children: "当前套餐"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 764,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 699,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 671,
                                                columnNumber: 15
                                            }, this)
                                        }, plan.id, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 670,
                                            columnNumber: 13
                                        }, this))
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 668,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    justify: "center",
                                    style: {
                                        marginTop: 20
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "订阅服务自动续费，可随时取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 784,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 783,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 638,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "确认退出登录",
                            open: logoutModalVisible,
                            onCancel: ()=>setLogoutModalVisible(false),
                            footer: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>setLogoutModalVisible(false),
                                    children: "取消"
                                }, "cancel", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 794,
                                    columnNumber: 11
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    danger: true,
                                    loading: logoutLoading,
                                    onClick: handleLogout,
                                    children: "确认退出"
                                }, "confirm", false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 797,
                                    columnNumber: 11
                                }, void 0)
                            ],
                            width: 400,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center',
                                    padding: '20px 0'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {
                                        style: {
                                            fontSize: 48,
                                            color: '#ff4d4f',
                                            marginBottom: 16
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 810,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginBottom: 8
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            style: {
                                                fontSize: 16
                                            },
                                            children: "您确定要退出登录吗？"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 812,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 811,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "退出后您需要重新登录才能继续使用系统"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 814,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 809,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 789,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(UserProfileCard, "72HXC8Jb/TbHsVLYoDQBUOLOP6Q=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = UserProfileCard;
            var _default = UserProfileCard;
            var _c;
            $RefreshReg$(_c, "UserProfileCard");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '17676891364031494242';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.13161455974651714885.hot-update.js.map