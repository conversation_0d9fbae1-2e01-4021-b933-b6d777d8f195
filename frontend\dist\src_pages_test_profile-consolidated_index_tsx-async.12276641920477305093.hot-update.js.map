{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.12276641920477305093.hot-update.js", "src/pages/test/profile-consolidated/UserProfileCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='7299713567161597675';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\r\n  CalendarOutlined,\r\n  CarOutlined,\r\n  CheckOutlined,\r\n  EditOutlined,\r\n  LogoutOutlined,\r\n  MailOutlined,\r\n  PhoneOutlined,\r\n  SettingOutlined,\r\n  TagOutlined,\r\n  UserOutlined,\r\n  WarningOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  Button,\r\n  Card,\r\n  Col,\r\n  Divider,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  Modal,\r\n  Row,\r\n  Space,\r\n  Steps,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n} from \"antd\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { UserService } from \"@/services/user\";\r\nimport { AuthService } from \"@/services\";\r\nimport { useModel, history } from '@umijs/max';\r\nimport type { UserPersonalStatsResponse, UserProfileDetailResponse } from \"@/types/api\";\r\n\r\nconst { Title, Text } = Typography;\r\nconst { Step } = Steps;\r\n\r\nconst UserProfileCard: React.FC = () => {\r\n  // 用户详细信息状态\r\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\r\n    name: \"\",\r\n    position: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n    telephone: \"\",\r\n    registerDate: \"\",\r\n    lastLoginTime: \"\",\r\n    lastLoginTeam: \"\",\r\n    teamCount: 0,\r\n    avatar: \"\",\r\n  });\r\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\r\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\r\n\r\n  // 个人统计数据状态\r\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\r\n    vehicles: 0,\r\n    personnel: 0,\r\n    warnings: 0,\r\n    alerts: 0,\r\n  });\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [statsError, setStatsError] = useState<string | null>(null);\r\n\r\n  // 订阅计划数据\r\n  const subscriptionPlans = [\r\n    {\r\n      id: \"basic\",\r\n      name: \"基础版\",\r\n      price: 0,\r\n      description: \"适合小团队使用\",\r\n      features: [\"最多5个团队\", \"最多20辆车辆\", \"基础安全监控\", \"基本报告功能\"],\r\n    },\r\n    {\r\n      id: \"professional\",\r\n      name: \"专业版\",\r\n      price: 199,\r\n      description: \"适合中小型企业\",\r\n      features: [\r\n        \"最多20个团队\",\r\n        \"最多100辆车辆\",\r\n        \"高级安全监控\",\r\n        \"详细分析报告\",\r\n        \"设备状态预警\",\r\n        \"优先技术支持\",\r\n      ],\r\n    },\r\n    {\r\n      id: \"enterprise\",\r\n      name: \"企业版\",\r\n      price: 499,\r\n      description: \"适合大型企业\",\r\n      features: [\r\n        \"不限团队数量\",\r\n        \"不限车辆数量\",\r\n        \"AI安全分析\",\r\n        \"实时监控告警\",\r\n        \"定制化报告\",\r\n        \"专属客户经理\",\r\n        \"24/7技术支持\",\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // 当前订阅信息\r\n  const currentSubscription = {\r\n    planId: \"basic\",\r\n    expires: \"2025-12-31\",\r\n  };\r\n\r\n  // 状态管理\r\n  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);\r\n  const [subscriptionModalVisible, setSubscriptionModalVisible] =\r\n    useState(false);\r\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\r\n  const [logoutLoading, setLogoutLoading] = useState(false);\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [editProfileForm] = Form.useForm();\r\n\r\n  const { setInitialState } = useModel('@@initialState');\r\n\r\n  // 获取用户数据\r\n  useEffect(() => {\r\n    console.log('UserProfileCard: useEffect 开始执行');\r\n\r\n    const fetchUserData = async () => {\r\n      try {\r\n        console.log('UserProfileCard: 开始获取用户数据');\r\n\r\n        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个\r\n        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {\r\n          console.error('获取用户详细信息失败:', error);\r\n          setUserInfoError('获取用户详细信息失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const statsPromise = UserService.getUserPersonalStats().catch(error => {\r\n          console.error('获取统计数据失败:', error);\r\n          setStatsError('获取统计数据失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);\r\n\r\n        if (userDetail) {\r\n          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);\r\n          setUserInfo(userDetail);\r\n          setUserInfoError(null);\r\n        }\r\n\r\n        if (stats) {\r\n          console.log('UserProfileCard: 获取到统计数据:', stats);\r\n          setPersonalStats(stats);\r\n          setStatsError(null);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('获取用户数据时发生未知错误:', error);\r\n        setUserInfoError('获取用户数据失败，请刷新页面重试');\r\n        setStatsError('获取统计数据失败，请刷新页面重试');\r\n      } finally {\r\n        setUserInfoLoading(false);\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserData();\r\n  }, []);\r\n\r\n  // 退出登录处理函数\r\n  const handleLogout = async () => {\r\n    try {\r\n      setLogoutLoading(true);\r\n\r\n      // 调用退出登录API\r\n      await AuthService.logout();\r\n\r\n      // 清除 initialState\r\n      if (setInitialState) {\r\n        await setInitialState({\r\n          currentUser: undefined,\r\n          currentTeam: undefined,\r\n        });\r\n      }\r\n\r\n      // 跳转到登录页面\r\n      history.push('/user/login');\r\n\r\n    } catch (error) {\r\n      console.error('退出登录失败:', error);\r\n      // 即使API调用失败，也要清除本地状态并跳转\r\n      if (setInitialState) {\r\n        await setInitialState({\r\n          currentUser: undefined,\r\n          currentTeam: undefined,\r\n        });\r\n      }\r\n      history.push('/user/login');\r\n    } finally {\r\n      setLogoutLoading(false);\r\n      setLogoutModalVisible(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Card\r\n        className=\"dashboard-card\"\r\n        style={{\r\n          borderRadius: 16,\r\n          boxShadow: \"0 4px 20px rgba(0,0,0,0.08)\",\r\n          border: \"1px solid rgba(0,0,0,0.06)\",\r\n          background: \"#ffffff\",\r\n          position: \"relative\",\r\n          overflow: \"hidden\",\r\n        }}\r\n        styles={{ body: { padding: 24 } }}\r\n      >\r\n        {/* 操作按钮区域 */}\r\n        <div\r\n          style={{\r\n            position: \"absolute\",\r\n            top: 20,\r\n            right: 20,\r\n            zIndex: 10,\r\n            display: \"flex\",\r\n            gap: 8,\r\n          }}\r\n        >\r\n          <Tooltip title=\"设置\">\r\n            <Dropdown\r\n              menu={{\r\n                items: [\r\n                  {\r\n                    key: \"editProfile\",\r\n                    icon: <EditOutlined />,\r\n                    label: \"修改资料\",\r\n                    onClick: () => {\r\n                      setEditProfileModalVisible(true);\r\n                      setCurrentStep(0);\r\n                      editProfileForm.setFieldsValue({\r\n                        name: userInfo.name,\r\n                        email: userInfo.email,\r\n                        telephone: userInfo.phone || userInfo.telephone,\r\n                      });\r\n                    },\r\n                  },\r\n                  {\r\n                    key: \"subscription\",\r\n                    icon: <TagOutlined />,\r\n                    label: \"订阅套餐\",\r\n                    onClick: () => setSubscriptionModalVisible(true),\r\n                  },\r\n                ],\r\n              }}\r\n              trigger={[\"click\"]}\r\n              placement=\"bottomRight\"\r\n            >\r\n              <Button\r\n                type=\"text\"\r\n                shape=\"circle\"\r\n                icon={<SettingOutlined />}\r\n                style={{\r\n                  color: \"#8c8c8c\",\r\n                  backgroundColor: \"rgba(0,0,0,0.04)\",\r\n                  border: \"none\",\r\n                  transition: \"all 0.2s\",\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = \"rgba(0,0,0,0.08)\";\r\n                  e.currentTarget.style.color = \"#595959\";\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = \"rgba(0,0,0,0.04)\";\r\n                  e.currentTarget.style.color = \"#8c8c8c\";\r\n                }}\r\n              />\r\n            </Dropdown>\r\n          </Tooltip>\r\n\r\n          <Tooltip title=\"退出登录\">\r\n            <Button\r\n              type=\"text\"\r\n              shape=\"circle\"\r\n              icon={<LogoutOutlined />}\r\n              onClick={() => setLogoutModalVisible(true)}\r\n              style={{\r\n                color: \"#8c8c8c\",\r\n                backgroundColor: \"rgba(0,0,0,0.04)\",\r\n                border: \"none\",\r\n                transition: \"all 0.2s\",\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                e.currentTarget.style.backgroundColor = \"rgba(255,77,79,0.1)\";\r\n                e.currentTarget.style.color = \"#ff4d4f\";\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.currentTarget.style.backgroundColor = \"rgba(0,0,0,0.04)\";\r\n                e.currentTarget.style.color = \"#8c8c8c\";\r\n              }}\r\n            />\r\n          </Tooltip>\r\n        </div>\r\n\r\n        {/* 主要内容区域 */}\r\n        <div style={{ paddingRight: 70 }}>\r\n          {userInfoError ? (\r\n            <Alert\r\n              message=\"用户信息加载失败\"\r\n              description={userInfoError}\r\n              type=\"error\"\r\n              showIcon\r\n              style={{ marginBottom: 24 }}\r\n            />\r\n          ) : (\r\n            <Spin spinning={userInfoLoading}>\r\n              {/* 用户基本信息区域 */}\r\n              <Row gutter={24} style={{ marginBottom: 24 }}>\r\n                {/* 左侧：用户名和联系信息 */}\r\n                <Col xs={24} md={12}>\r\n                  <div style={{ height: \"100%\" }}>\r\n                    <Title\r\n                      level={2}\r\n                      style={{\r\n                        margin: 0,\r\n                        marginBottom: 16,\r\n                        fontSize: 28,\r\n                        fontWeight: 600,\r\n                        color: \"#262626\",\r\n                      }}\r\n                    >\r\n                      {userInfo.name || \"加载中...\"}\r\n                    </Title>\r\n\r\n                    {/* 联系信息 */}\r\n                    <div style={{ marginBottom: 16 }}>\r\n                      <Space direction=\"vertical\" size={8} style={{ width: \"100%\" }}>\r\n                        {userInfo.email && (\r\n                          <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                            <MailOutlined style={{ color: \"#1890ff\", marginRight: 8, fontSize: 16 }} />\r\n                            <Text style={{ fontSize: 14, color: \"#595959\" }}>\r\n                              {userInfo.email}\r\n                            </Text>\r\n                          </div>\r\n                        )}\r\n                        {(userInfo.phone || userInfo.telephone) && (\r\n                          <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                            <PhoneOutlined style={{ color: \"#1890ff\", marginRight: 8, fontSize: 16 }} />\r\n                            <Text style={{ fontSize: 14, color: \"#595959\" }}>\r\n                              {userInfo.phone || userInfo.telephone}\r\n                            </Text>\r\n                          </div>\r\n                        )}\r\n                        {userInfo.registerDate && (\r\n                          <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                            <CalendarOutlined style={{ color: \"#8c8c8c\", marginRight: 8, fontSize: 16 }} />\r\n                            <Text style={{ fontSize: 14, color: \"#8c8c8c\" }}>\r\n                              注册于 {userInfo.registerDate}\r\n                            </Text>\r\n                          </div>\r\n                        )}\r\n                      </Space>\r\n                    </div>\r\n                  </div>\r\n                </Col>\r\n\r\n                {/* 右侧：登录信息 */}\r\n                <Col xs={24} md={12}>\r\n                  <div\r\n                    style={{\r\n                      backgroundColor: \"#f8fafc\",\r\n                      borderRadius: 12,\r\n                      padding: 20,\r\n                      height: \"100%\",\r\n                      border: \"1px solid #e2e8f0\",\r\n                    }}\r\n                  >\r\n                    <Title level={5} style={{ margin: 0, marginBottom: 16, color: \"#374151\" }}>\r\n                      登录信息\r\n                    </Title>\r\n                    <Space direction=\"vertical\" size={12} style={{ width: \"100%\" }}>\r\n                      <div>\r\n                        <div style={{ display: \"flex\", alignItems: \"center\", marginBottom: 4 }}>\r\n                          <CalendarOutlined style={{ color: \"#6b7280\", marginRight: 6, fontSize: 14 }} />\r\n                          <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                            最后登录时间\r\n                          </Text>\r\n                        </div>\r\n                        <Text strong style={{ fontSize: 14, color: \"#374151\" }}>\r\n                          {userInfo.lastLoginTime || \"暂无记录\"}\r\n                        </Text>\r\n                      </div>\r\n                      <div>\r\n                        <div style={{ display: \"flex\", alignItems: \"center\", marginBottom: 4 }}>\r\n                          <UserOutlined style={{ color: \"#6b7280\", marginRight: 6, fontSize: 14 }} />\r\n                          <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                            最后登录团队\r\n                          </Text>\r\n                        </div>\r\n                        <Text strong style={{ fontSize: 14, color: \"#374151\" }}>\r\n                          {userInfo.lastLoginTeam || \"暂无记录\"}\r\n                        </Text>\r\n                      </div>\r\n                    </Space>\r\n                  </div>\r\n                </Col>\r\n              </Row>\r\n\r\n              {/* 统计数据区域 */}\r\n              <div>\r\n                <div style={{ marginBottom: 20 }}>\r\n                  <Title level={4} style={{ margin: 0, color: \"#262626\" }}>\r\n                    数据统计\r\n                  </Title>\r\n                  <Text type=\"secondary\" style={{ fontSize: 14 }}>\r\n                    实时监控数据概览\r\n                  </Text>\r\n                </div>\r\n                {statsError ? (\r\n                  <Alert\r\n                    message=\"统计数据加载失败\"\r\n                    description={statsError}\r\n                    type=\"error\"\r\n                    showIcon\r\n                  />\r\n                ) : (\r\n                  <Spin spinning={statsLoading}>\r\n                    <Row gutter={16}>\r\n                      {/* 车辆统计 */}\r\n                      <Col xs={12} sm={6}>\r\n                        <div\r\n                          style={{\r\n                            backgroundColor: \"#f0f9ff\",\r\n                            borderRadius: 12,\r\n                            padding: 20,\r\n                            textAlign: \"center\",\r\n                            border: \"1px solid #e6f7ff\",\r\n                            height: \"100%\",\r\n                            transition: \"all 0.3s ease\",\r\n                          }}\r\n                          onMouseEnter={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\r\n                            e.currentTarget.style.boxShadow = \"0 8px 25px rgba(24,144,255,0.15)\";\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(0)\";\r\n                            e.currentTarget.style.boxShadow = \"none\";\r\n                          }}\r\n                        >\r\n                          <CarOutlined\r\n                            style={{\r\n                              fontSize: 24,\r\n                              color: \"#1890ff\",\r\n                              marginBottom: 8,\r\n                            }}\r\n                          />\r\n                          <div>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 32,\r\n                                fontWeight: 700,\r\n                                color: \"#1890ff\",\r\n                                display: \"block\",\r\n                                lineHeight: 1,\r\n                              }}\r\n                            >\r\n                              {personalStats.vehicles}\r\n                            </Text>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 14,\r\n                                color: \"#595959\",\r\n                                marginTop: 6,\r\n                                fontWeight: 500,\r\n                              }}\r\n                            >\r\n                              车辆\r\n                            </Text>\r\n                          </div>\r\n                        </div>\r\n                      </Col>\r\n\r\n                      {/* 人员统计 */}\r\n                      <Col xs={12} sm={6}>\r\n                        <div\r\n                          style={{\r\n                            backgroundColor: \"#f6ffed\",\r\n                            borderRadius: 12,\r\n                            padding: 20,\r\n                            textAlign: \"center\",\r\n                            border: \"1px solid #d9f7be\",\r\n                            height: \"100%\",\r\n                            transition: \"all 0.3s ease\",\r\n                          }}\r\n                          onMouseEnter={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\r\n                            e.currentTarget.style.boxShadow = \"0 8px 25px rgba(82,196,26,0.15)\";\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(0)\";\r\n                            e.currentTarget.style.boxShadow = \"none\";\r\n                          }}\r\n                        >\r\n                          <UserOutlined\r\n                            style={{\r\n                              fontSize: 24,\r\n                              color: \"#52c41a\",\r\n                              marginBottom: 8,\r\n                            }}\r\n                          />\r\n                          <div>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 32,\r\n                                fontWeight: 700,\r\n                                color: \"#52c41a\",\r\n                                display: \"block\",\r\n                                lineHeight: 1,\r\n                              }}\r\n                            >\r\n                              {personalStats.personnel}\r\n                            </Text>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 14,\r\n                                color: \"#595959\",\r\n                                marginTop: 6,\r\n                                fontWeight: 500,\r\n                              }}\r\n                            >\r\n                              人员\r\n                            </Text>\r\n                          </div>\r\n                        </div>\r\n                      </Col>\r\n\r\n                      {/* 预警统计 */}\r\n                      <Col xs={12} sm={6}>\r\n                        <div\r\n                          style={{\r\n                            backgroundColor: \"#fffbe6\",\r\n                            borderRadius: 12,\r\n                            padding: 20,\r\n                            textAlign: \"center\",\r\n                            border: \"1px solid #ffe58f\",\r\n                            height: \"100%\",\r\n                            transition: \"all 0.3s ease\",\r\n                          }}\r\n                          onMouseEnter={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\r\n                            e.currentTarget.style.boxShadow = \"0 8px 25px rgba(250,173,20,0.15)\";\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(0)\";\r\n                            e.currentTarget.style.boxShadow = \"none\";\r\n                          }}\r\n                        >\r\n                          <WarningOutlined\r\n                            style={{\r\n                              fontSize: 24,\r\n                              color: \"#faad14\",\r\n                              marginBottom: 8,\r\n                            }}\r\n                          />\r\n                          <div>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 32,\r\n                                fontWeight: 700,\r\n                                color: \"#faad14\",\r\n                                display: \"block\",\r\n                                lineHeight: 1,\r\n                              }}\r\n                            >\r\n                              {personalStats.warnings}\r\n                            </Text>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 14,\r\n                                color: \"#595959\",\r\n                                marginTop: 6,\r\n                                fontWeight: 500,\r\n                              }}\r\n                            >\r\n                              预警\r\n                            </Text>\r\n                          </div>\r\n                        </div>\r\n                      </Col>\r\n\r\n                      {/* 告警统计 */}\r\n                      <Col xs={12} sm={6}>\r\n                        <div\r\n                          style={{\r\n                            backgroundColor: \"#fff2f0\",\r\n                            borderRadius: 12,\r\n                            padding: 20,\r\n                            textAlign: \"center\",\r\n                            border: \"1px solid #ffccc7\",\r\n                            height: \"100%\",\r\n                            transition: \"all 0.3s ease\",\r\n                          }}\r\n                          onMouseEnter={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(-2px)\";\r\n                            e.currentTarget.style.boxShadow = \"0 8px 25px rgba(255,77,79,0.15)\";\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            e.currentTarget.style.transform = \"translateY(0)\";\r\n                            e.currentTarget.style.boxShadow = \"none\";\r\n                          }}\r\n                        >\r\n                          <WarningOutlined\r\n                            style={{\r\n                              fontSize: 24,\r\n                              color: \"#ff4d4f\",\r\n                              marginBottom: 8,\r\n                            }}\r\n                          />\r\n                          <div>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 32,\r\n                                fontWeight: 700,\r\n                                color: \"#ff4d4f\",\r\n                                display: \"block\",\r\n                                lineHeight: 1,\r\n                              }}\r\n                            >\r\n                              {personalStats.alerts}\r\n                            </Text>\r\n                            <Text\r\n                              style={{\r\n                                fontSize: 14,\r\n                                color: \"#595959\",\r\n                                marginTop: 6,\r\n                                fontWeight: 500,\r\n                              }}\r\n                            >\r\n                              告警\r\n                            </Text>\r\n                          </div>\r\n                        </div>\r\n                      </Col>\r\n                    </Row>\r\n                  </Spin>\r\n                )}\r\n              </div>\r\n            </Spin>\r\n          )}\r\n        </div>\r\n      </Card>\r\n\r\n      {/* 修改资料模态框 */}\r\n      <Modal\r\n        title=\"修改个人资料\"\r\n        open={editProfileModalVisible}\r\n        onCancel={() => {\r\n          setEditProfileModalVisible(false);\r\n          setCurrentStep(0);\r\n        }}\r\n        footer={[\r\n          currentStep === 1 && (\r\n            <Button key=\"back\" onClick={() => setCurrentStep(0)}>\r\n              上一步\r\n            </Button>\r\n          ),\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              if (currentStep === 0) {\r\n                editProfileForm.validateFields().then(() => {\r\n                  setCurrentStep(1);\r\n                });\r\n              } else {\r\n                editProfileForm.validateFields().then((values) => {\r\n                  console.log(\"个人资料表单值:\", values);\r\n                  // 提交表单，这里简化处理，只输出到控制台\r\n                  setEditProfileModalVisible(false);\r\n                  setCurrentStep(0);\r\n                });\r\n              }\r\n            }}\r\n          >\r\n            {currentStep === 0 ? \"下一步\" : \"确定\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Steps current={currentStep} style={{ marginBottom: 16 }}>\r\n          <Step title=\"填写信息\" />\r\n          <Step title=\"安全验证\" />\r\n        </Steps>\r\n\r\n        <Form form={editProfileForm} layout=\"vertical\" requiredMark={false}>\r\n          {currentStep === 0 ? (\r\n            <>\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"用户名\"\r\n                rules={[{ required: true, message: \"请输入用户名\" }]}\r\n              >\r\n                <Input placeholder=\"请输入用户名\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"邮箱\"\r\n                rules={[\r\n                  { required: true, message: \"请输入邮箱地址\" },\r\n                  { type: \"email\", message: \"请输入有效的邮箱地址\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入邮箱地址\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"telephone\"\r\n                label=\"手机号\"\r\n                rules={[\r\n                  { required: true, message: \"请输入手机号\" },\r\n                  { pattern: /^1\\d{10}$/, message: \"请输入有效的手机号\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入手机号\" />\r\n              </Form.Item>\r\n            </>\r\n          ) : (\r\n            <div style={{ textAlign: \"center\" }}>\r\n              <div style={{ margin: \"12px 0\", textAlign: \"center\" }}>\r\n                <Text>\r\n                  验证码已发送至您的手机号{\" \"}\r\n                  <Text strong>{editProfileForm.getFieldValue(\"telephone\")}</Text>\r\n                </Text>\r\n              </div>\r\n              <Form.Item\r\n                name=\"verificationCode\"\r\n                label=\"验证码\"\r\n                rules={[{ required: true, message: \"请输入验证码\" }]}\r\n              >\r\n                <Input\r\n                  placeholder=\"请输入6位验证码\"\r\n                  maxLength={6}\r\n                  style={{ width: \"50%\", textAlign: \"center\" }}\r\n                />\r\n              </Form.Item>\r\n              <Button type=\"link\" style={{ padding: 0 }}>\r\n                重新发送验证码\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 订阅套餐模态框 */}\r\n      <Modal\r\n        title=\"订阅套餐\"\r\n        open={subscriptionModalVisible}\r\n        onCancel={() => setSubscriptionModalVisible(false)}\r\n        footer={null}\r\n        width={800}\r\n      >\r\n        <div\r\n          style={{\r\n            background: \"#f9f9f9\",\r\n            padding: 12,\r\n            borderRadius: 8,\r\n            marginBottom: 16,\r\n          }}\r\n        >\r\n          <Flex justify=\"space-between\" align=\"center\">\r\n            <Text strong>当前套餐: </Text>\r\n            <Tag color=\"green\" style={{ marginLeft: 8, fontSize: 13 }}>\r\n              {\r\n                subscriptionPlans.find(\r\n                  (p) => p.id === currentSubscription.planId\r\n                )?.name\r\n              }\r\n            </Tag>\r\n            <Text type=\"secondary\">\r\n              到期时间: {currentSubscription.expires}\r\n            </Text>\r\n          </Flex>\r\n        </div>\r\n\r\n        <Row gutter={24}>\r\n          {subscriptionPlans.map((plan) => (\r\n            <Col span={8} key={plan.id}>\r\n              <div\r\n                style={{\r\n                  height: \"100%\",\r\n                  borderRadius: 8,\r\n                  border: `1px solid ${\r\n                    plan.id === currentSubscription.planId\r\n                      ? \"#52c41a\"\r\n                      : \"#d9d9d9\"\r\n                  }`,\r\n                  background: \"#fff\",\r\n                  position: \"relative\",\r\n                  overflow: \"hidden\",\r\n                }}\r\n              >\r\n                {plan.id === currentSubscription.planId && (\r\n                  <Tag\r\n                    color=\"green\"\r\n                    style={{\r\n                      position: \"absolute\",\r\n                      top: -10,\r\n                      right: -10,\r\n                      borderRadius: 2,\r\n                      boxShadow: \"0 2px 8px rgba(0,0,0,0.1)\",\r\n                    }}\r\n                  >\r\n                    当前套餐\r\n                  </Tag>\r\n                )}\r\n                <div style={{ padding: 16 }}>\r\n                  <Title\r\n                    level={4}\r\n                    style={{ textAlign: \"center\", margin: \"12px 0 8px\" }}\r\n                  >\r\n                    {plan.name}\r\n                  </Title>\r\n                  <Flex vertical align=\"center\" style={{ marginBottom: 12 }}>\r\n                    {plan.price > 0 ? (\r\n                      <>\r\n                        <Title level={2} style={{ marginBottom: 0 }}>\r\n                          ¥{plan.price}\r\n                        </Title>\r\n                        <Text type=\"secondary\">/月</Text>\r\n                      </>\r\n                    ) : (\r\n                      <Title\r\n                        level={2}\r\n                        style={{ color: \"#52c41a\", marginBottom: 0 }}\r\n                      >\r\n                        免费\r\n                      </Title>\r\n                    )}\r\n                    <Text type=\"secondary\" style={{ marginTop: 4 }}>\r\n                      {plan.description}\r\n                    </Text>\r\n                  </Flex>\r\n\r\n                  <Divider style={{ margin: \"8px 0\" }} />\r\n\r\n                  <div style={{ minHeight: 170 }}>\r\n                    {plan.features.map((feature, index) => (\r\n                      <Space\r\n                        key={index}\r\n                        align=\"start\"\r\n                        style={{ marginBottom: 6 }}\r\n                      >\r\n                        <CheckOutlined\r\n                          style={{\r\n                            color: \"#52c41a\",\r\n                            marginRight: 8,\r\n                            marginTop: 4,\r\n                          }}\r\n                        />\r\n                        <Text>{feature}</Text>\r\n                      </Space>\r\n                    ))}\r\n                  </div>\r\n\r\n                  {plan.id !== currentSubscription.planId ? (\r\n                    <Button\r\n                      type=\"primary\"\r\n                      block\r\n                      style={{\r\n                        marginTop: 12,\r\n                        boxShadow: \"0 2px 8px rgba(24, 144, 255, 0.3)\",\r\n                      }}\r\n                      onClick={() => {\r\n                        console.log(\"选择套餐:\", plan);\r\n                        setSubscriptionModalVisible(false);\r\n                      }}\r\n                    >\r\n                      立即订阅\r\n                    </Button>\r\n                  ) : (\r\n                    <Button\r\n                      block\r\n                      style={{\r\n                        marginTop: 12,\r\n                        background: \"#f6ffed\",\r\n                        borderColor: \"#b7eb8f\",\r\n                        color: \"#389e0d\",\r\n                      }}\r\n                      disabled\r\n                    >\r\n                      当前套餐\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </Col>\r\n          ))}\r\n        </Row>\r\n\r\n        <Flex justify=\"center\" style={{ marginTop: 20 }}>\r\n          <Text type=\"secondary\">订阅服务自动续费，可随时取消</Text>\r\n        </Flex>\r\n      </Modal>\r\n\r\n      {/* 退出登录确认模态框 */}\r\n      <Modal\r\n        title=\"确认退出登录\"\r\n        open={logoutModalVisible}\r\n        onCancel={() => setLogoutModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"cancel\" onClick={() => setLogoutModalVisible(false)}>\r\n            取消\r\n          </Button>,\r\n          <Button\r\n            key=\"confirm\"\r\n            type=\"primary\"\r\n            danger\r\n            loading={logoutLoading}\r\n            onClick={handleLogout}\r\n          >\r\n            确认退出\r\n          </Button>,\r\n        ]}\r\n        width={400}\r\n      >\r\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\r\n          <LogoutOutlined style={{ fontSize: 48, color: '#ff4d4f', marginBottom: 16 }} />\r\n          <div style={{ marginBottom: 8 }}>\r\n            <Text strong style={{ fontSize: 16 }}>您确定要退出登录吗？</Text>\r\n          </div>\r\n          <Text type=\"secondary\">\r\n            退出后您需要重新登录才能继续使用系统\r\n          </Text>\r\n        </div>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default UserProfileCard;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCC06Bb;;;2BAAA;;;;;;0CAj6BO;yCAmBA;oFACoC;yCACf;6CACA;wCACM;;;;;;;;;;YAGlC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,IAAI,EAAE,GAAG,WAAK;YAEtB,MAAM,kBAA4B;oBA8tBlB;;gBA7tBd,WAAW;gBACX,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,WAAW;gBACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,MAAM,oBAAoB;oBACxB;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BAAC;4BAAU;4BAAW;4BAAU;yBAAS;oBACrD;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;iBACD;gBAED,SAAS;gBACT,MAAM,sBAAsB;oBAC1B,QAAQ;oBACR,SAAS;gBACX;gBAEA,OAAO;gBACP,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,IAAA,eAAQ,EAAC;gBACvE,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,IAAA,eAAQ,EAAC;gBACX,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;gBACnD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;gBAC/C,MAAM,CAAC,gBAAgB,GAAG,UAAI,CAAC,OAAO;gBAEtC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAErC,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,QAAQ,GAAG,CAAC;oBAEZ,MAAM,gBAAgB;wBACpB,IAAI;4BACF,QAAQ,GAAG,CAAC;4BAEZ,8BAA8B;4BAC9B,MAAM,oBAAoB,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;gCACjE,QAAQ,KAAK,CAAC,eAAe;gCAC7B,iBAAiB;gCACjB,OAAO;4BACT;4BAEA,MAAM,eAAe,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;gCAC5D,QAAQ,KAAK,CAAC,aAAa;gCAC3B,cAAc;gCACd,OAAO;4BACT;4BAEA,MAAM,CAAC,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAAC;gCAAmB;6BAAa;4BAE/E,IAAI,YAAY;gCACd,QAAQ,GAAG,CAAC,+BAA+B;gCAC3C,YAAY;gCACZ,iBAAiB;4BACnB;4BAEA,IAAI,OAAO;gCACT,QAAQ,GAAG,CAAC,6BAA6B;gCACzC,iBAAiB;gCACjB,cAAc;4BAChB;wBAEF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kBAAkB;4BAChC,iBAAiB;4BACjB,cAAc;wBAChB,SAAU;4BACR,mBAAmB;4BACnB,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,WAAW;gBACX,MAAM,eAAe;oBACnB,IAAI;wBACF,iBAAiB;wBAEjB,YAAY;wBACZ,MAAM,qBAAW,CAAC,MAAM;wBAExB,kBAAkB;wBAClB,IAAI,iBACF,MAAM,gBAAgB;4BACpB,aAAa;4BACb,aAAa;wBACf;wBAGF,UAAU;wBACV,YAAO,CAAC,IAAI,CAAC;oBAEf,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,wBAAwB;wBACxB,IAAI,iBACF,MAAM,gBAAgB;4BACpB,aAAa;4BACb,aAAa;wBACf;wBAEF,YAAO,CAAC,IAAI,CAAC;oBACf,SAAU;wBACR,iBAAiB;wBACjB,sBAAsB;oBACxB;gBACF;gBAEA,qBACE;;sCACE,2BAAC,UAAI;4BACH,WAAU;4BACV,OAAO;gCACL,cAAc;gCACd,WAAW;gCACX,QAAQ;gCACR,YAAY;gCACZ,UAAU;gCACV,UAAU;4BACZ;4BACA,QAAQ;gCAAE,MAAM;oCAAE,SAAS;gCAAG;4BAAE;;8CAGhC,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,KAAK;wCACL,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,KAAK;oCACP;;sDAEA,2BAAC,aAAO;4CAAC,OAAM;sDACb,cAAA,2BAAC,cAAQ;gDACP,MAAM;oDACJ,OAAO;wDACL;4DACE,KAAK;4DACL,oBAAM,2BAAC,mBAAY;;;;;4DACnB,OAAO;4DACP,SAAS;gEACP,2BAA2B;gEAC3B,eAAe;gEACf,gBAAgB,cAAc,CAAC;oEAC7B,MAAM,SAAS,IAAI;oEACnB,OAAO,SAAS,KAAK;oEACrB,WAAW,SAAS,KAAK,IAAI,SAAS,SAAS;gEACjD;4DACF;wDACF;wDACA;4DACE,KAAK;4DACL,oBAAM,2BAAC,kBAAW;;;;;4DAClB,OAAO;4DACP,SAAS,IAAM,4BAA4B;wDAC7C;qDACD;gDACH;gDACA,SAAS;oDAAC;iDAAQ;gDAClB,WAAU;0DAEV,cAAA,2BAAC,YAAM;oDACL,MAAK;oDACL,OAAM;oDACN,oBAAM,2BAAC,sBAAe;;;;;oDACtB,OAAO;wDACL,OAAO;wDACP,iBAAiB;wDACjB,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;;;;;;;;;;;;;;;;sDAKN,2BAAC,aAAO;4CAAC,OAAM;sDACb,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,OAAM;gDACN,oBAAM,2BAAC,qBAAc;;;;;gDACrB,SAAS,IAAM,sBAAsB;gDACrC,OAAO;oDACL,OAAO;oDACP,iBAAiB;oDACjB,QAAQ;oDACR,YAAY;gDACd;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;;;;;;;;;;;;;;;;;8CAMN,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC5B,8BACC,2BAAC,WAAK;wCACJ,SAAQ;wCACR,aAAa;wCACb,MAAK;wCACL,QAAQ;wCACR,OAAO;4CAAE,cAAc;wCAAG;;;;;6DAG5B,2BAAC,UAAI;wCAAC,UAAU;;0DAEd,2BAAC,SAAG;gDAAC,QAAQ;gDAAI,OAAO;oDAAE,cAAc;gDAAG;;kEAEzC,2BAAC,SAAG;wDAAC,IAAI;wDAAI,IAAI;kEACf,cAAA,2BAAC;4DAAI,OAAO;gEAAE,QAAQ;4DAAO;;8EAC3B,2BAAC;oEACC,OAAO;oEACP,OAAO;wEACL,QAAQ;wEACR,cAAc;wEACd,UAAU;wEACV,YAAY;wEACZ,OAAO;oEACT;8EAEC,SAAS,IAAI,IAAI;;;;;;8EAIpB,2BAAC;oEAAI,OAAO;wEAAE,cAAc;oEAAG;8EAC7B,cAAA,2BAAC,WAAK;wEAAC,WAAU;wEAAW,MAAM;wEAAG,OAAO;4EAAE,OAAO;wEAAO;;4EACzD,SAAS,KAAK,kBACb,2BAAC;gFAAI,OAAO;oFAAE,SAAS;oFAAQ,YAAY;gFAAS;;kGAClD,2BAAC,mBAAY;wFAAC,OAAO;4FAAE,OAAO;4FAAW,aAAa;4FAAG,UAAU;wFAAG;;;;;;kGACtE,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;kGAC3C,SAAS,KAAK;;;;;;;;;;;;4EAInB,CAAA,SAAS,KAAK,IAAI,SAAS,SAAS,AAAD,mBACnC,2BAAC;gFAAI,OAAO;oFAAE,SAAS;oFAAQ,YAAY;gFAAS;;kGAClD,2BAAC,oBAAa;wFAAC,OAAO;4FAAE,OAAO;4FAAW,aAAa;4FAAG,UAAU;wFAAG;;;;;;kGACvE,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;kGAC3C,SAAS,KAAK,IAAI,SAAS,SAAS;;;;;;;;;;;;4EAI1C,SAAS,YAAY,kBACpB,2BAAC;gFAAI,OAAO;oFAAE,SAAS;oFAAQ,YAAY;gFAAS;;kGAClD,2BAAC,uBAAgB;wFAAC,OAAO;4FAAE,OAAO;4FAAW,aAAa;4FAAG,UAAU;wFAAG;;;;;;kGAC1E,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;;4FAAG;4FAC1C,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAUxC,2BAAC,SAAG;wDAAC,IAAI;wDAAI,IAAI;kEACf,cAAA,2BAAC;4DACC,OAAO;gEACL,iBAAiB;gEACjB,cAAc;gEACd,SAAS;gEACT,QAAQ;gEACR,QAAQ;4DACV;;8EAEA,2BAAC;oEAAM,OAAO;oEAAG,OAAO;wEAAE,QAAQ;wEAAG,cAAc;wEAAI,OAAO;oEAAU;8EAAG;;;;;;8EAG3E,2BAAC,WAAK;oEAAC,WAAU;oEAAW,MAAM;oEAAI,OAAO;wEAAE,OAAO;oEAAO;;sFAC3D,2BAAC;;8FACC,2BAAC;oFAAI,OAAO;wFAAE,SAAS;wFAAQ,YAAY;wFAAU,cAAc;oFAAE;;sGACnE,2BAAC,uBAAgB;4FAAC,OAAO;gGAAE,OAAO;gGAAW,aAAa;gGAAG,UAAU;4FAAG;;;;;;sGAC1E,2BAAC;4FAAK,MAAK;4FAAY,OAAO;gGAAE,UAAU;4FAAG;sGAAG;;;;;;;;;;;;8FAIlD,2BAAC;oFAAK,MAAM;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;oFAAU;8FAClD,SAAS,aAAa,IAAI;;;;;;;;;;;;sFAG/B,2BAAC;;8FACC,2BAAC;oFAAI,OAAO;wFAAE,SAAS;wFAAQ,YAAY;wFAAU,cAAc;oFAAE;;sGACnE,2BAAC,mBAAY;4FAAC,OAAO;gGAAE,OAAO;gGAAW,aAAa;gGAAG,UAAU;4FAAG;;;;;;sGACtE,2BAAC;4FAAK,MAAK;4FAAY,OAAO;gGAAE,UAAU;4FAAG;sGAAG;;;;;;;;;;;;8FAIlD,2BAAC;oFAAK,MAAM;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;oFAAU;8FAClD,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DASvC,2BAAC;;kEACC,2BAAC;wDAAI,OAAO;4DAAE,cAAc;wDAAG;;0EAC7B,2BAAC;gEAAM,OAAO;gEAAG,OAAO;oEAAE,QAAQ;oEAAG,OAAO;gEAAU;0EAAG;;;;;;0EAGzD,2BAAC;gEAAK,MAAK;gEAAY,OAAO;oEAAE,UAAU;gEAAG;0EAAG;;;;;;;;;;;;oDAIjD,2BACC,2BAAC,WAAK;wDACJ,SAAQ;wDACR,aAAa;wDACb,MAAK;wDACL,QAAQ;;;;;6EAGV,2BAAC,UAAI;wDAAC,UAAU;kEACd,cAAA,2BAAC,SAAG;4DAAC,QAAQ;;8EAEX,2BAAC,SAAG;oEAAC,IAAI;oEAAI,IAAI;8EACf,cAAA,2BAAC;wEACC,OAAO;4EACL,iBAAiB;4EACjB,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,QAAQ;4EACR,QAAQ;4EACR,YAAY;wEACd;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;;0FAEA,2BAAC,kBAAW;gFACV,OAAO;oFACL,UAAU;oFACV,OAAO;oFACP,cAAc;gFAChB;;;;;;0FAEF,2BAAC;;kGACC,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,YAAY;4FACZ,OAAO;4FACP,SAAS;4FACT,YAAY;wFACd;kGAEC,cAAc,QAAQ;;;;;;kGAEzB,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,WAAW;4FACX,YAAY;wFACd;kGACD;;;;;;;;;;;;;;;;;;;;;;;8EAQP,2BAAC,SAAG;oEAAC,IAAI;oEAAI,IAAI;8EACf,cAAA,2BAAC;wEACC,OAAO;4EACL,iBAAiB;4EACjB,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,QAAQ;4EACR,QAAQ;4EACR,YAAY;wEACd;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;;0FAEA,2BAAC,mBAAY;gFACX,OAAO;oFACL,UAAU;oFACV,OAAO;oFACP,cAAc;gFAChB;;;;;;0FAEF,2BAAC;;kGACC,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,YAAY;4FACZ,OAAO;4FACP,SAAS;4FACT,YAAY;wFACd;kGAEC,cAAc,SAAS;;;;;;kGAE1B,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,WAAW;4FACX,YAAY;wFACd;kGACD;;;;;;;;;;;;;;;;;;;;;;;8EAQP,2BAAC,SAAG;oEAAC,IAAI;oEAAI,IAAI;8EACf,cAAA,2BAAC;wEACC,OAAO;4EACL,iBAAiB;4EACjB,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,QAAQ;4EACR,QAAQ;4EACR,YAAY;wEACd;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;;0FAEA,2BAAC,sBAAe;gFACd,OAAO;oFACL,UAAU;oFACV,OAAO;oFACP,cAAc;gFAChB;;;;;;0FAEF,2BAAC;;kGACC,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,YAAY;4FACZ,OAAO;4FACP,SAAS;4FACT,YAAY;wFACd;kGAEC,cAAc,QAAQ;;;;;;kGAEzB,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,WAAW;4FACX,YAAY;wFACd;kGACD;;;;;;;;;;;;;;;;;;;;;;;8EAQP,2BAAC,SAAG;oEAAC,IAAI;oEAAI,IAAI;8EACf,cAAA,2BAAC;wEACC,OAAO;4EACL,iBAAiB;4EACjB,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,QAAQ;4EACR,QAAQ;4EACR,YAAY;wEACd;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4EAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEACpC;;0FAEA,2BAAC,sBAAe;gFACd,OAAO;oFACL,UAAU;oFACV,OAAO;oFACP,cAAc;gFAChB;;;;;;0FAEF,2BAAC;;kGACC,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,YAAY;4FACZ,OAAO;4FACP,SAAS;4FACT,YAAY;wFACd;kGAEC,cAAc,MAAM;;;;;;kGAEvB,2BAAC;wFACC,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,WAAW;4FACX,YAAY;wFACd;kGACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAgBvB,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,2BAA2B;gCAC3B,eAAe;4BACjB;4BACA,QAAQ;gCACN,gBAAgB,mBACd,2BAAC,YAAM;oCAAY,SAAS,IAAM,eAAe;8CAAI;mCAAzC;;;;;8CAId,2BAAC,YAAM;oCAEL,MAAK;oCACL,SAAS;wCACP,IAAI,gBAAgB,GAClB,gBAAgB,cAAc,GAAG,IAAI,CAAC;4CACpC,eAAe;wCACjB;6CAEA,gBAAgB,cAAc,GAAG,IAAI,CAAC,CAAC;4CACrC,QAAQ,GAAG,CAAC,YAAY;4CACxB,sBAAsB;4CACtB,2BAA2B;4CAC3B,eAAe;wCACjB;oCAEJ;8CAEC,gBAAgB,IAAI,QAAQ;mCAjBzB;;;;;6BAmBP;;8CAED,2BAAC,WAAK;oCAAC,SAAS;oCAAa,OAAO;wCAAE,cAAc;oCAAG;;sDACrD,2BAAC;4CAAK,OAAM;;;;;;sDACZ,2BAAC;4CAAK,OAAM;;;;;;;;;;;;8CAGd,2BAAC,UAAI;oCAAC,MAAM;oCAAiB,QAAO;oCAAW,cAAc;8CAC1D,gBAAgB,kBACf;;0DACE,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAS;iDAAE;0DAE9C,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;0DAErB,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDACL;wDAAE,UAAU;wDAAM,SAAS;oDAAU;oDACrC;wDAAE,MAAM;wDAAS,SAAS;oDAAa;iDACxC;0DAED,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;0DAErB,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDACL;wDAAE,UAAU;wDAAM,SAAS;oDAAS;oDACpC;wDAAE,SAAS;wDAAa,SAAS;oDAAY;iDAC9C;0DAED,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;;qEAIvB,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAS;;0DAChC,2BAAC;gDAAI,OAAO;oDAAE,QAAQ;oDAAU,WAAW;gDAAS;0DAClD,cAAA,2BAAC;;wDAAK;wDACS;sEACb,2BAAC;4DAAK,MAAM;sEAAE,gBAAgB,aAAa,CAAC;;;;;;;;;;;;;;;;;0DAGhD,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAS;iDAAE;0DAE9C,cAAA,2BAAC,WAAK;oDACJ,aAAY;oDACZ,WAAW;oDACX,OAAO;wDAAE,OAAO;wDAAO,WAAW;oDAAS;;;;;;;;;;;0DAG/C,2BAAC,YAAM;gDAAC,MAAK;gDAAO,OAAO;oDAAE,SAAS;gDAAE;0DAAG;;;;;;;;;;;;;;;;;;;;;;;sCASnD,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,4BAA4B;4BAC5C,QAAQ;4BACR,OAAO;;8CAEP,2BAAC;oCACC,OAAO;wCACL,YAAY;wCACZ,SAAS;wCACT,cAAc;wCACd,cAAc;oCAChB;8CAEA,cAAA,2BAAC,UAAI;wCAAC,SAAQ;wCAAgB,OAAM;;0DAClC,2BAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,2BAAC,SAAG;gDAAC,OAAM;gDAAQ,OAAO;oDAAE,YAAY;oDAAG,UAAU;gDAAG;2DAEpD,0BAAA,kBAAkB,IAAI,CACpB,CAAC,IAAM,EAAE,EAAE,KAAK,oBAAoB,MAAM,eAD5C,8CAAA,wBAEG,IAAI;;;;;;0DAGX,2BAAC;gDAAK,MAAK;;oDAAY;oDACd,oBAAoB,OAAO;;;;;;;;;;;;;;;;;;8CAKxC,2BAAC,SAAG;oCAAC,QAAQ;8CACV,kBAAkB,GAAG,CAAC,CAAC,qBACtB,2BAAC,SAAG;4CAAC,MAAM;sDACT,cAAA,2BAAC;gDACC,OAAO;oDACL,QAAQ;oDACR,cAAc;oDACd,QAAQ,CAAC,UAAU,EACjB,KAAK,EAAE,KAAK,oBAAoB,MAAM,GAClC,YACA,UACL,CAAC;oDACF,YAAY;oDACZ,UAAU;oDACV,UAAU;gDACZ;;oDAEC,KAAK,EAAE,KAAK,oBAAoB,MAAM,kBACrC,2BAAC,SAAG;wDACF,OAAM;wDACN,OAAO;4DACL,UAAU;4DACV,KAAK;4DACL,OAAO;4DACP,cAAc;4DACd,WAAW;wDACb;kEACD;;;;;;kEAIH,2BAAC;wDAAI,OAAO;4DAAE,SAAS;wDAAG;;0EACxB,2BAAC;gEACC,OAAO;gEACP,OAAO;oEAAE,WAAW;oEAAU,QAAQ;gEAAa;0EAElD,KAAK,IAAI;;;;;;0EAEZ,2BAAC,UAAI;gEAAC,QAAQ;gEAAC,OAAM;gEAAS,OAAO;oEAAE,cAAc;gEAAG;;oEACrD,KAAK,KAAK,GAAG,kBACZ;;0FACE,2BAAC;gFAAM,OAAO;gFAAG,OAAO;oFAAE,cAAc;gFAAE;;oFAAG;oFACzC,KAAK,KAAK;;;;;;;0FAEd,2BAAC;gFAAK,MAAK;0FAAY;;;;;;;qGAGzB,2BAAC;wEACC,OAAO;wEACP,OAAO;4EAAE,OAAO;4EAAW,cAAc;wEAAE;kFAC5C;;;;;;kFAIH,2BAAC;wEAAK,MAAK;wEAAY,OAAO;4EAAE,WAAW;wEAAE;kFAC1C,KAAK,WAAW;;;;;;;;;;;;0EAIrB,2BAAC,aAAO;gEAAC,OAAO;oEAAE,QAAQ;gEAAQ;;;;;;0EAElC,2BAAC;gEAAI,OAAO;oEAAE,WAAW;gEAAI;0EAC1B,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,2BAAC,WAAK;wEAEJ,OAAM;wEACN,OAAO;4EAAE,cAAc;wEAAE;;0FAEzB,2BAAC,oBAAa;gFACZ,OAAO;oFACL,OAAO;oFACP,aAAa;oFACb,WAAW;gFACb;;;;;;0FAEF,2BAAC;0FAAM;;;;;;;uEAXF;;;;;;;;;;4DAgBV,KAAK,EAAE,KAAK,oBAAoB,MAAM,iBACrC,2BAAC,YAAM;gEACL,MAAK;gEACL,KAAK;gEACL,OAAO;oEACL,WAAW;oEACX,WAAW;gEACb;gEACA,SAAS;oEACP,QAAQ,GAAG,CAAC,SAAS;oEACrB,4BAA4B;gEAC9B;0EACD;;;;;qFAID,2BAAC,YAAM;gEACL,KAAK;gEACL,OAAO;oEACL,WAAW;oEACX,YAAY;oEACZ,aAAa;oEACb,OAAO;gEACT;gEACA,QAAQ;0EACT;;;;;;;;;;;;;;;;;;2CAvGU,KAAK,EAAE;;;;;;;;;;8CAiH9B,2BAAC,UAAI;oCAAC,SAAQ;oCAAS,OAAO;wCAAE,WAAW;oCAAG;8CAC5C,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;sCAK3B,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,sBAAsB;4BACtC,QAAQ;8CACN,2BAAC,YAAM;oCAAc,SAAS,IAAM,sBAAsB;8CAAQ;mCAAtD;;;;;8CAGZ,2BAAC,YAAM;oCAEL,MAAK;oCACL,MAAM;oCACN,SAAS;oCACT,SAAS;8CACV;mCALK;;;;;6BAQP;4BACD,OAAO;sCAEP,cAAA,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAU,SAAS;gCAAS;;kDACnD,2BAAC,qBAAc;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;4CAAW,cAAc;wCAAG;;;;;;kDAC1E,2BAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAE;kDAC5B,cAAA,2BAAC;4CAAK,MAAM;4CAAC,OAAO;gDAAE,UAAU;4CAAG;sDAAG;;;;;;;;;;;kDAExC,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;;;YAOjC;eAl4BM;;oBAgFsB,UAAI,CAAC;oBAEH,aAAQ;;;iBAlFhC;gBAo4BN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID16BD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}