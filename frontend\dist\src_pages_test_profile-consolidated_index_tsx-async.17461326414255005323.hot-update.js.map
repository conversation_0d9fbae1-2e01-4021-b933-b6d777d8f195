{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.17461326414255005323.hot-update.js", "src/pages/test/profile-consolidated/UserProfileCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15109464735480349106';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import React from 'react';\r\nimport { Card, Row, Col, Avatar, Statistic, Divider } from 'antd';\r\nimport { \r\n  UserOutlined, \r\n  MailOutlined, \r\n  NumberOutlined,\r\n  CalendarOutlined,\r\n  ClockCircleOutlined,\r\n  TeamOutlined,\r\n  CarOutlined,\r\n  UserAddOutlined,\r\n  BellOutlined,\r\n  ExclamationCircleOutlined\r\n} from '@ant-design/icons';\r\n\r\nconst UserDashboard = () => {\r\n  return (\r\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\r\n      <Card style={{ width: '100%', borderRadius: '12px', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)' }}>\r\n        {/* 头部标题 */}\r\n        <div style={{ marginBottom: '24px' }}>\r\n          <h1 style={{ fontSize: '24px', fontWeight: 600, marginBottom: '8px', color: '#2c3e50' }}>用户信息</h1>\r\n          <p style={{ color: '#7f8c8d' }}>查看用户账户的详细信息和相关统计</p>\r\n        </div>\r\n\r\n        <Row gutter={24}>\r\n          {/* 左侧用户信息区 */}\r\n          <Col span={16}>\r\n            <Card \r\n              style={{ width: '100%', border: 'none', boxShadow: 'none' }}\r\n              bodyStyle={{ padding: '24px' }}\r\n            >\r\n              {/* 用户头像和基本信息 */}\r\n              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '30px' }}>\r\n                <Avatar \r\n                  style={{ \r\n                    backgroundColor: '#1890ff',\r\n                    fontSize: '24px',\r\n                    width: '80px',\r\n                    height: '80px',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}\r\n                >\r\n                  Z\r\n                </Avatar>\r\n                <div style={{ marginLeft: '24px' }}>\r\n                  <h2 style={{ fontSize: '24px', fontWeight: 600, marginBottom: '4px', color: '#2c3e50' }}>\r\n                    zhangsan\r\n                  </h2>\r\n                  <div style={{ display: 'flex', alignItems: 'center', color: '#7f8c8d' }}>\r\n                    <MailOutlined style={{ marginRight: '8px' }} />\r\n                    <span><EMAIL></span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <Divider style={{ margin: '24px 0' }} />\r\n\r\n              {/* 用户详细信息 */}\r\n              <Row gutter={[24, 24]}>\r\n                <Col span={12}>\r\n                  <DetailCard \r\n                    icon={<NumberOutlined />}\r\n                    title=\"用户编号\"\r\n                    value=\"123456\"\r\n                  />\r\n                </Col>\r\n                \r\n                <Col span={12}>\r\n                  <DetailCard \r\n                    icon={<CalendarOutlined />}\r\n                    title=\"注册日期\"\r\n                    value=\"2025-07-23\"\r\n                  />\r\n                </Col>\r\n                \r\n                <Col span={12}>\r\n                  <DetailCard \r\n                    icon={<TeamOutlined />}\r\n                    title=\"团队总数\"\r\n                    value=\"2\"\r\n                  />\r\n                </Col>\r\n                \r\n                <Col span={12}>\r\n                  <DetailCard \r\n                    icon={<ClockCircleOutlined />}\r\n                    title=\"最后登录时间\"\r\n                    value=\"2025-07-28 10:44:20\"\r\n                  />\r\n                </Col>\r\n                \r\n                <Col span={12}>\r\n                  <DetailCard \r\n                    icon={<UserOutlined />}\r\n                    title=\"最后登录团队\"\r\n                    value=\"123\"\r\n                  />\r\n                </Col>\r\n              </Row>\r\n            </Card>\r\n          </Col>\r\n\r\n          {/* 右侧统计卡片区 */}\r\n          <Col span={8}>\r\n            <Row gutter={[0, 24]}>\r\n              <Col span={24}>\r\n                <StatCard \r\n                  icon={<CarOutlined style={{ color: '#3498db', fontSize: '36px' }} />}\r\n                  title=\"车辆\"\r\n                  value={0}\r\n                />\r\n              </Col>\r\n              \r\n              <Col span={24}>\r\n                <StatCard \r\n                  icon={<UserAddOutlined style={{ color: '#2ecc71', fontSize: '36px' }} />}\r\n                  title=\"人员\"\r\n                  value={0}\r\n                />\r\n              </Col>\r\n              \r\n              <Col span={24}>\r\n                <StatCard \r\n                  icon={<BellOutlined style={{ color: '#f39c12', fontSize: '36px' }} />}\r\n                  title=\"预警\"\r\n                  value={0}\r\n                />\r\n              </Col>\r\n              \r\n              <Col span={24}>\r\n                <StatCard \r\n                  icon={<ExclamationCircleOutlined style={{ color: '#e74c3c', fontSize: '36px' }} />}\r\n                  title=\"告警\"\r\n                  value={0}\r\n                />\r\n              </Col>\r\n            </Row>\r\n          </Col>\r\n        </Row>\r\n\r\n        {/* 页脚信息 */}\r\n        <div style={{ marginTop: '36px', color: '#7f8c8d', fontSize: '14px', textAlign: 'center' }}>\r\n          © 2025 用户信息管理系统 | 最后更新: 2025-07-28\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\n// 自定义信息卡片组件\r\nconst DetailCard = ({ icon, title, value }) => (\r\n  <div style={{ \r\n    display: 'flex', \r\n    alignItems: 'center', \r\n    backgroundColor: '#f8f9fa', \r\n    padding: '12px', \r\n    borderRadius: '8px'\r\n  }}>\r\n    <div style={{ \r\n      width: '40px', \r\n      height: '40px', \r\n      backgroundColor: '#e9ecef', \r\n      borderRadius: '6px', \r\n      display: 'flex', \r\n      alignItems: 'center', \r\n      justifyContent: 'center',\r\n      marginRight: '12px',\r\n      fontSize: '18px',\r\n      color: '#495057'\r\n    }}>\r\n      {icon}\r\n    </div>\r\n    <div>\r\n      <div style={{ color: '#6c757d', fontSize: '14px' }}>{title}</div>\r\n      <div style={{ fontWeight: 500, fontSize: '18px', color: '#212529' }}>{value}</div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\n// 自定义统计卡片组件\r\nconst StatCard = ({ icon, title, value }) => (\r\n  <Card \r\n    style={{ \r\n      borderRadius: '12px', \r\n      textAlign: 'center',\r\n      transition: 'transform 0.3s',\r\n    }}\r\n    hoverable\r\n  >\r\n    <div style={{ marginBottom: '16px' }}>{icon}</div>\r\n    <Statistic title={title} value={value} />\r\n  </Card>\r\n);\r\n\r\nexport default UserDashboard;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCkMb;;;2BAAA;;;;;;;mFArMkB;yCACyC;0CAYpD;;;;;;;;;YAEP,MAAM,gBAAgB;gBACpB,qBACE,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,UAAU;wBAAU,QAAQ;oBAAS;8BAClE,cAAA,2BAAC,UAAI;wBAAC,OAAO;4BAAE,OAAO;4BAAQ,cAAc;4BAAQ,WAAW;wBAAiC;;0CAE9F,2BAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAO;;kDACjC,2BAAC;wCAAG,OAAO;4CAAE,UAAU;4CAAQ,YAAY;4CAAK,cAAc;4CAAO,OAAO;wCAAU;kDAAG;;;;;;kDACzF,2BAAC;wCAAE,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;0CAGlC,2BAAC,SAAG;gCAAC,QAAQ;;kDAEX,2BAAC,SAAG;wCAAC,MAAM;kDACT,cAAA,2BAAC,UAAI;4CACH,OAAO;gDAAE,OAAO;gDAAQ,QAAQ;gDAAQ,WAAW;4CAAO;4CAC1D,WAAW;gDAAE,SAAS;4CAAO;;8DAG7B,2BAAC;oDAAI,OAAO;wDAAE,SAAS;wDAAQ,YAAY;wDAAU,cAAc;oDAAO;;sEACxE,2BAAC,YAAM;4DACL,OAAO;gEACL,iBAAiB;gEACjB,UAAU;gEACV,OAAO;gEACP,QAAQ;gEACR,SAAS;gEACT,YAAY;gEACZ,gBAAgB;4DAClB;sEACD;;;;;;sEAGD,2BAAC;4DAAI,OAAO;gEAAE,YAAY;4DAAO;;8EAC/B,2BAAC;oEAAG,OAAO;wEAAE,UAAU;wEAAQ,YAAY;wEAAK,cAAc;wEAAO,OAAO;oEAAU;8EAAG;;;;;;8EAGzF,2BAAC;oEAAI,OAAO;wEAAE,SAAS;wEAAQ,YAAY;wEAAU,OAAO;oEAAU;;sFACpE,2BAAC,mBAAY;4EAAC,OAAO;gFAAE,aAAa;4EAAM;;;;;;sFAC1C,2BAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;8DAKZ,2BAAC,aAAO;oDAAC,OAAO;wDAAE,QAAQ;oDAAS;;;;;;8DAGnC,2BAAC,SAAG;oDAAC,QAAQ;wDAAC;wDAAI;qDAAG;;sEACnB,2BAAC,SAAG;4DAAC,MAAM;sEACT,cAAA,2BAAC;gEACC,oBAAM,2BAAC,qBAAc;;;;;gEACrB,OAAM;gEACN,OAAM;;;;;;;;;;;sEAIV,2BAAC,SAAG;4DAAC,MAAM;sEACT,cAAA,2BAAC;gEACC,oBAAM,2BAAC,uBAAgB;;;;;gEACvB,OAAM;gEACN,OAAM;;;;;;;;;;;sEAIV,2BAAC,SAAG;4DAAC,MAAM;sEACT,cAAA,2BAAC;gEACC,oBAAM,2BAAC,mBAAY;;;;;gEACnB,OAAM;gEACN,OAAM;;;;;;;;;;;sEAIV,2BAAC,SAAG;4DAAC,MAAM;sEACT,cAAA,2BAAC;gEACC,oBAAM,2BAAC,0BAAmB;;;;;gEAC1B,OAAM;gEACN,OAAM;;;;;;;;;;;sEAIV,2BAAC,SAAG;4DAAC,MAAM;sEACT,cAAA,2BAAC;gEACC,oBAAM,2BAAC,mBAAY;;;;;gEACnB,OAAM;gEACN,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQhB,2BAAC,SAAG;wCAAC,MAAM;kDACT,cAAA,2BAAC,SAAG;4CAAC,QAAQ;gDAAC;gDAAG;6CAAG;;8DAClB,2BAAC,SAAG;oDAAC,MAAM;8DACT,cAAA,2BAAC;wDACC,oBAAM,2BAAC,kBAAW;4DAAC,OAAO;gEAAE,OAAO;gEAAW,UAAU;4DAAO;;;;;;wDAC/D,OAAM;wDACN,OAAO;;;;;;;;;;;8DAIX,2BAAC,SAAG;oDAAC,MAAM;8DACT,cAAA,2BAAC;wDACC,oBAAM,2BAAC,sBAAe;4DAAC,OAAO;gEAAE,OAAO;gEAAW,UAAU;4DAAO;;;;;;wDACnE,OAAM;wDACN,OAAO;;;;;;;;;;;8DAIX,2BAAC,SAAG;oDAAC,MAAM;8DACT,cAAA,2BAAC;wDACC,oBAAM,2BAAC,mBAAY;4DAAC,OAAO;gEAAE,OAAO;gEAAW,UAAU;4DAAO;;;;;;wDAChE,OAAM;wDACN,OAAO;;;;;;;;;;;8DAIX,2BAAC,SAAG;oDAAC,MAAM;8DACT,cAAA,2BAAC;wDACC,oBAAM,2BAAC,gCAAyB;4DAAC,OAAO;gEAAE,OAAO;gEAAW,UAAU;4DAAO;;;;;;wDAC7E,OAAM;wDACN,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQjB,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAQ,OAAO;oCAAW,UAAU;oCAAQ,WAAW;gCAAS;0CAAG;;;;;;;;;;;;;;;;;YAMpG;iBAvIM;YAyIN,YAAY;YACZ,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACxC,2BAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,YAAY;wBACZ,iBAAiB;wBACjB,SAAS;wBACT,cAAc;oBAChB;;sCACE,2BAAC;4BAAI,OAAO;gCACV,OAAO;gCACP,QAAQ;gCACR,iBAAiB;gCACjB,cAAc;gCACd,SAAS;gCACT,YAAY;gCACZ,gBAAgB;gCAChB,aAAa;gCACb,UAAU;gCACV,OAAO;4BACT;sCACG;;;;;;sCAEH,2BAAC;;8CACC,2BAAC;oCAAI,OAAO;wCAAE,OAAO;wCAAW,UAAU;oCAAO;8CAAI;;;;;;8CACrD,2BAAC;oCAAI,OAAO;wCAAE,YAAY;wCAAK,UAAU;wCAAQ,OAAO;oCAAU;8CAAI;;;;;;;;;;;;;;;;;;kBAxBtE;YA6BN,YAAY;YACZ,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACtC,2BAAC,UAAI;oBACH,OAAO;wBACL,cAAc;wBACd,WAAW;wBACX,YAAY;oBACd;oBACA,SAAS;;sCAET,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAO;sCAAI;;;;;;sCACvC,2BAAC,eAAS;4BAAC,OAAO;4BAAO,OAAO;;;;;;;;;;;;kBAV9B;gBAcN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDlMD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}